Shader "UI/Scene/Task/Intro"
{
	Properties
	{
		[HideInInspector]_MainTex ("Sprite Texture", 2D) = "white" {}
		[HideInInspector]_Color ("Tint", Color) = (1,1,1,1)
		
		[HideInInspector]_StencilComp ("Stencil Comparison", Float) = 8
		[HideInInspector]_Stencil ("Stencil ID", Float) = 0
		[HideInInspector]_StencilOp ("Stencil Operation", Float) = 0
		[HideInInspector]_StencilWriteMask ("Stencil Write Mask", Float) = 255
		[HideInInspector]_StencilReadMask ("Stencil Read Mask", Float) = 255

		[HideInInspector]_ColorMask ("Color Mask", Float) = 15
		[HideInInspector]_ClipRect ("Clip Rect", Vector) = (-32767, -32767, 32767, 32767)
		
		_Noise ("Noise", 2D) = "white" {}
		
		[Toggle(_STRAIGHT_ALPHA_INPUT)] _StraightAlphaInput("Straight Alpha Texture", Int) = 0
		_lightDistortion ("Light Distorion", Range(0.75, 2.0)) = 1.5
		_Alpha ("Fade Out Alpha", Range(0.5, 2.5)) = 0.5
	}

	SubShader
	{
		Tags
		{ 
			"Queue"="Transparent" 
			"IgnoreProjector"="True" 
			"RenderType"="Transparent" 
			"PreviewType"="Plane"
			"CanUseSpriteAtlas"="True"
		}
		
		Stencil
		{
			Ref [_Stencil]
			Comp [_StencilComp]
			Pass [_StencilOp] 
			ReadMask [_StencilReadMask]
			WriteMask [_StencilWriteMask]
		}

		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [unity_GUIZTestMode]
		Fog { Mode Off }
		Blend One OneMinusSrcAlpha

		ColorMask [_ColorMask]

		Pass
		{
		CGPROGRAM
			// #define  _CANVAS_GROUP_COMPATIBLE
			#pragma vertex vert
			#pragma fragment frag
			#pragma target 2.0
			#include "UnityCG.cginc"
			#include "UnityUI.cginc"
			// #define  UNITY_UI_ALPHACLIP
			#pragma - _STRAIGHT_ALPHA_INPUT
			#include "Assets/Spine/Runtime/spine-unity/Shaders/CGIncludes/Spine-Common.cginc"
			// #include "Assets/Spine/Runtime/spine-unity/Shaders/SkeletonGraphic/CGIncludes/Spine-SkeletonGraphic-NormalPass.cginc"

			#define TAU 6.283185307179586
			
			struct appdata_t
			{
				float4 vertex   : POSITION;
				float2 texcoord : TEXCOORD0;
				float4 color : COLOR;
			};

			struct v2f
			{
				float4 vertex   : SV_POSITION;
				half2 uv  : TEXCOORD0;
				float4 worldPosition : TEXCOORD1;
				float4 color : COLOR;
			};
			
			sampler2D _MainTex;
			sampler2D _Noise;
			float4 _MainTex_ST;
			float4 _ClipRect;
			float _lightDistortion;
			float _Alpha;
			fixed4 _TextureSampleAdd;

			v2f vert(appdata_t IN)
			{
				v2f OUT;
				
				UNITY_SETUP_INSTANCE_ID(IN);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
				
				OUT.worldPosition = IN.vertex;
				OUT.vertex = UnityObjectToClipPos(IN.vertex);
				OUT.uv = TRANSFORM_TEX(IN.texcoord, _MainTex);
				
				#ifdef UNITY_HALF_TEXEL_OFFSET
				OUT.vertex.xy += (_ScreenParams.zw-1.0) * float2(-1,1);
				#endif
				
#ifdef _CANVAS_GROUP_COMPATIBLE
				float4 vertexColor = IN.color;
				vertexColor.rgb *= vertexColor.a;
#else
				float4 vertexColor = PMAGammaToTargetSpaceSaturated(float4(TargetToGammaSpace(IN.color.rgb), IN.color.a));
#endif
				OUT.color = PMAGammaToTargetSpace(IN.color);
				return OUT;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				float4 color = tex2D(_MainTex, i.uv);
				
				#if defined(_STRAIGHT_ALPHA_INPUT)
				color.rgb *= color.a;
				#endif
				
				color *= i.color;
				float4 noise = tex2D(_Noise, i.uv * _lightDistortion);
#ifdef UNITY_UI_ALPHACLIP
				clip (color.a - 0.01);
#endif
				const float width = _ClipRect.z - _ClipRect.x;
				const float height = _ClipRect.w - _ClipRect.y;

				const float position_x = (i.worldPosition.x - _ClipRect.x) / width;
				const float position_y = (i.worldPosition.y - _ClipRect.y) / height;
				const float2 uv = float2(position_x, position_y);

				const float alpha = saturate(1 - uv.y);
				color.a *= saturate(lerp(color.a + _Alpha, 0, alpha));
				
				color.a *= UnityGet2DClipping(i.worldPosition.xy, _ClipRect);
				color.rgb *= color.a;

                return color;
			}
			
		ENDCG
		}
	}
}
