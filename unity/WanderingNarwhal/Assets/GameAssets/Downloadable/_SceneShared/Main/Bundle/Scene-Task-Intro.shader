Shader "UI/Scene/Task/Intro"
{
	Properties
	{
		[HideInInspector]_MainTex ("Sprite Texture", 2D) = "white" {}
		[HideInInspector]_Color ("Tint", Color) = (1,1,1,1)
		[HideInInspector]_AdditiveAmount ("AdditiveAmount", Float) = 2
		
		[HideInInspector]_StencilComp ("Stencil Comparison", Float) = 8
		[HideInInspector]_Stencil ("Stencil ID", Float) = 0
		[HideInInspector]_StencilOp ("Stencil Operation", Float) = 0
		[HideInInspector]_StencilWriteMask ("Stencil Write Mask", Float) = 255
		[HideInInspector]_StencilReadMask ("Stencil Read Mask", Float) = 255

		[HideInInspector]_ColorMask ("Color Mask", Float) = 15
		[HideInInspector]_ClipRect ("Clip Rect", Vector) = (-32767, -32767, 32767, 32767)
		
		_Noise ("Noise", 2D) = "white" {}
		_NoiseIntensity ("NoiseIntensity", Float) = 15
		
		_RemoveInitialAlpha("Remove Initial Alpha", Float) = 0
		[Toggle(_STRAIGHT_ALPHA_INPUT)] _StraightAlphaInput("Straight Alpha Texture", Int) = 0
		_LightPosition ("Light Start Position", Range(0.0, 1.0)) = 0.4
		_lightDistortion ("Light Distorion", Range(0.75, 2.0)) = 1.5
		_LightIntensityInverted ("Light Intensity Inverted", Range(1, 4)) = 1
		_Alpha ("Fade Out Alpha", Range(0.5, 2.5)) = 0.5
	}

	SubShader
	{
		Tags
		{ 
			"Queue"="Transparent" 
			"IgnoreProjector"="True" 
			"RenderType"="Transparent" 
			"PreviewType"="Plane"
			"CanUseSpriteAtlas"="True"
		}
		
		Stencil
		{
			Ref [_Stencil]
			Comp [_StencilComp]
			Pass [_StencilOp] 
			ReadMask [_StencilReadMask]
			WriteMask [_StencilWriteMask]
		}

		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [unity_GUIZTestMode]
		Fog { Mode Off }
		Blend One OneMinusSrcAlpha

		ColorMask [_ColorMask]

		Pass
		{
		CGPROGRAM
			// #define  _CANVAS_GROUP_COMPATIBLE
			#pragma vertex vert
			#pragma fragment frag
			#pragma target 2.0
			#include "UnityCG.cginc"
			#include "UnityUI.cginc"
			// #define  UNITY_UI_ALPHACLIP
			#pragma - _STRAIGHT_ALPHA_INPUT
			#include "Assets/Spine/Runtime/spine-unity/Shaders/CGIncludes/Spine-Common.cginc"
			// #include "Assets/Spine/Runtime/spine-unity/Shaders/SkeletonGraphic/CGIncludes/Spine-SkeletonGraphic-NormalPass.cginc"

			#define TAU 6.283185307179586
			
			struct appdata_t
			{
				float4 vertex   : POSITION;
				float2 texcoord : TEXCOORD0;
				float4 color : COLOR;
			};

			struct v2f
			{
				float4 vertex   : SV_POSITION;
				half2 uv  : TEXCOORD0;
				float4 worldPosition : TEXCOORD1;
				float4 color : COLOR;
			};
			
			sampler2D _MainTex;
			sampler2D _Noise;
			float4 _MainTex_ST;
			float4 _ClipRect;
			float _LightPosition;
			float _lightDistortion;
			float _LightIntensityInverted;
			float _Alpha;
			fixed4 _TextureSampleAdd;
			float _NoiseIntensity;
			float _AdditiveAmount;
			float _RemoveInitialAlpha;

			v2f vert(appdata_t IN)
			{
				v2f OUT;
				
				UNITY_SETUP_INSTANCE_ID(IN);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
				
				OUT.worldPosition = IN.vertex;
				OUT.vertex = UnityObjectToClipPos(IN.vertex);
				OUT.uv = TRANSFORM_TEX(IN.texcoord, _MainTex);
				
				#ifdef UNITY_HALF_TEXEL_OFFSET
				OUT.vertex.xy += (_ScreenParams.zw-1.0) * float2(-1,1);
				#endif
				
#ifdef _CANVAS_GROUP_COMPATIBLE
				float4 vertexColor = IN.color;
				vertexColor.rgb *= vertexColor.a;
#else
				float4 vertexColor = PMAGammaToTargetSpaceSaturated(float4(TargetToGammaSpace(IN.color.rgb), IN.color.a));
#endif
				OUT.color = PMAGammaToTargetSpace(IN.color);
				return OUT;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				float4 color = tex2D(_MainTex, i.uv);

				float alpha_remove = 1.0 / color.a;
				alpha_remove = 1.0 - _RemoveInitialAlpha + _RemoveInitialAlpha * alpha_remove;
				color.rgb *= alpha_remove;
				
				#if defined(_STRAIGHT_ALPHA_INPUT)
				color.rgb *= color.a;
				#endif
				
				color *= i.color;
				float4 noise = tex2D(_Noise, i.uv * _lightDistortion);
#ifdef UNITY_UI_ALPHACLIP
				clip (color.a - 0.01);
#endif
				const float width = _ClipRect.z - _ClipRect.x;
				const float height = _ClipRect.w - _ClipRect.y;

				const float position_x = (i.worldPosition.x - _ClipRect.x) / width;
				const float2 uv = float2(position_x, (i.worldPosition.y - _ClipRect.y) / height);
				const float position_y = (i.worldPosition.y - (_ClipRect.y + height * _LightPosition)) / height;

				const float noise_interval = 0.1;
				const float noise_amount = lerp(-noise_interval, noise_interval, noise.r * _NoiseIntensity);

				const float3 yellow = float3(1, 1, 0);
				const float3 white = float3(1, 1, 1);
				const float gradient = saturate(position_y);
				const float additive = pow(1 - abs(lerp(0, _LightIntensityInverted, gradient) - 0.5) * _AdditiveAmount, _AdditiveAmount);
				color.rgb += saturate(noise_amount * lerp(yellow, white, additive) * additive);

				const float alpha = saturate(uv.y);
				color.a *= saturate(lerp(color.a + _Alpha, 0, alpha));
				
				color.a *= UnityGet2DClipping(i.worldPosition.xy, _ClipRect);
				color.rgb *= color.a;

                return color;
			}
			
		ENDCG
		}
	}
}
