#if UNITY_EDITOR
using RPC.Teams;
using BBB.Match3.Systems.CreateSimulationSystems;
using System.Linq;
using BBB.Wallet;
using Bebopbee.Core.Systems.RpcCommandManager;
using System;
using System.Collections;
using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Controller;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.MMVibrations;
using BBB.RaceEvents;
using BBB.Social;
using BBB.UI.Core;
using BBB.UI.Level;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.Match3.Logic;
using BBB.MMVibrations.Plugins;
using BBB.RaceEvents.UI;
using BBB.Social.SignIn;
using BBB.TeamEvents;
using BebopBee.Social;
using GameAssets.Scripts.Theme;
using PBGame;
using RPC.VIP;
using UI;
using UnityEngine;
using Object = UnityEngine.Object;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Player;
using GameAssets.Scripts.Tutorial.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using Lidgren.Network;
using PBConfig;
using UniRx;
using DayMonthYear = PBConfig.DayMonthYear;
using Price = PBConfig.Price;

namespace BBB.UI
{
    public partial class EditorLevelControllerLauncher
    {
        private class AccountManagerStub : IAccountManager
        {
            public event Action<IPlayer> ProfileUpdated;
            public event Action OnAvatarChanged;
            public event Action TeamChanged;
            public event Action TeamDataChanged;
            public event Action<string> ProfileEmailUpdated;
            public event Action<BCUserRaceData[]> RemoteRaceEventsDataUpdated;
            public event Action<BCUserGameEventData[]> RemoteEventsDataUpdated;
            public event Action<BCTriviaChallenge> TriviaChallengeUpdated;
            public event Action Authenticated;

            public Profile Profile { get; private set; }
            public IPlayer LocalPlayer { get; }
            public bool RemoteProgressSelected { get; }
            public bool IsInTeam { get; }
            public bool IsAnyPlatformLoggedIn { get; }
            public bool JustMigrated { get; }
            public BCUserRaceData[] LastRaceEventData { get; }
            public BCUserGameEventData[] LastGameEventData { get; }
            public BCTriviaChallenge LastTriviaChallengeData { get; }

            public void UpdateLastGameEventDataFor(string gameEventUid, Action<BCUserGameEventData> callback, Action noEventCallback)
            {
            }

            public void ResetProgression()
            {
                
            }

            public void DeleteLocalPlayerData()
            {
                
            }

            public void LoginGrimes()
            {
            }

            public async UniTask<LoginResultData> LoginToPlatform(AccountType accountType)
            {
                return null;
            }

            public void Logout()
            {
            }

            public void SetTrivia(string currentTrivia, bool resetTrivia)
            {
            }

            public void LogoutFromPlatforms()
            {
            }
            
            public void LogoutFromPlatform(AccountType accountType)
            {
            }

            public void SendLoginCommand(bool applyChangesImmediately = false, Action<bool> success = null)
            {
            }

            public void SetTeamData(TeamData teamData, bool lastSocialModalOpening)
            {
                
            }

            public void ChangeNameAndAvatar(string newAvatar, string newDisplayName)
            {
            }

            public void HandleProfileUpdate(BCUpdatedUserProfile updatedProfileData)
            {
            }

            public void Restart()
            {
            }

            public UniTask LocalGameSaved(PlayerSavedGame savedGame)
            {
                return UniTask.CompletedTask;
            }

            public void TryUpdatePlayer(Action callback, bool shouldRestart = false)
            {
            }

            public void ValidatePlayerState(IPlayer player)
            {
            }

            public bool TryGetPlayerProgress(IPlayer player, out PlayerProgress result)
            {
                result = default;
                return false;
            }

            public void IncrementHelpCount(int amount = 1)
            {
            }

            public void ResetHelpCount()
            {
            }
            
            public void SetSdbState(bool enabled)
            {
            }
            
            public void SetChallengesState(bool enabled)
            {
            }

            public void DeleteProgression(Action onSuccess, Action onFailure)
            {
                
            }

            public List<AccountType> GetAuthSources()
            {
                throw new NotImplementedException();
            }

            public void UpdateEmailAndPhoneNumber(string email, string phoneNumber)
            {
            }

            public void UpdateAppleInfo(string id = "", string familyName = "", string givenName = "", string email = "")
            {
            }

            public void UpdateGoogleInfo(string id = "", string displayName = "", string avatar = "", string email = "")
            {
            }

            public void UpdateFacebookInfo(string id = "", string name = "", string avatar = "", string email = "")
            {
            }
        }

        public class AnalyticsReporterStub : ILevelAnalyticsReporter, IContextInitializable, IContextReleasable
        {
            private readonly ReplayLevelSystem _replayLevelSystem = new();

            private IPlayerManager _playerManager;
            private IEventDispatcher _eventDispatcher;
            private GameController _gameController;

            public void InitializeByContext(IContext context)
            {
                _playerManager = context.Resolve<IPlayerManager>();
                _eventDispatcher = context.Resolve<IEventDispatcher>();
                _gameController = context.Resolve<GameController>();

                _eventDispatcher.RemoveListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
                _eventDispatcher.AddListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
            }

            public void BoostUsed(string boosterId)
            {
            }

            public void RegisterPowerUpCreatedDuringGame()
            {
            }

            public void RegisterItemDestroyedOn(Tile tile)
            {
            }

            public void RegisterFirstMove()
            {
            }

            public void RegisterWastedMove()
            {
            }

            public void RegisterPlayerMove(NetRandomGenerationValues randomState, IPlayerInput playerInput)
            {
                _replayLevelSystem.RegisterPlayerMove(randomState, playerInput);
            }

            public void LevelStarted(ILevel level, IConfig config, string activeGameEvent)
            {
                _replayLevelSystem.RegisterStartLevel(level, config, activeGameEvent, _playerManager, _gameController);
            }

            public string GetReplayData()
            {
                var data = _replayLevelSystem.GetReplayDataAsString();
                return data;
            }

            public void ReportLevelEnded(LevelOutcome levelOutcome, ILevel level, int evShuffleCount, List<string> boostersToReport, AssistState originalAssistState, AssistState progressAchieved)
            {
            }

            public void RegisterAppliedAutoBoosters(List<AutoBoostInstance> appliedAutoBoosters)
            {
                if (appliedAutoBoosters.IsNullOrEmpty())
                    return;
                _replayLevelSystem.RegisterAppliedAutoBoosters(appliedAutoBoosters);
            }

            public void MovesBought(FBConfig.Price price)
            {
            }

            public void RegisterMovesAdded(NetRandomGenerationValues randomState, int addedMoves)
            {
            }

            public void ReportStuckEvent(ILevel level, string detailedStr)
            {
                BDebug.LogError(LogCat.Match3,GetReplayData());
            }

            public void RegisterSuperDiscoBallValueUpdate(NetRandomGenerationValues randomState, float updatedValue)
            {
                _replayLevelSystem.RegisterSuperDiscoBallValueUpdate(randomState, updatedValue);
            }

            private void RegisterSuperBoosterActivated(SuperBoostInvokedEvent superBoostInvokedEvent)
            {
                if (superBoostInvokedEvent.Arg1 != Coords.OutOfGrid)
                {
                    _replayLevelSystem.RegisterSuperBoostActivated(superBoostInvokedEvent.Arg0, superBoostInvokedEvent.Arg1);
                }
            }

            public void ReleaseByContext(IContext context)
            {
                _eventDispatcher.RemoveListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
            }
        }

        private class VibrationsWrapperStub : IVibrationsWrapper
        {
            public bool Enabled => false;

            public void SetEnabled(bool value)
            {
            }

            public void EnqueueHaptic(ImpactPreset type, int id)
            {
            }

            public void PlayHaptic(ImpactPreset type)
            {
            }
        }

        private class OverlayDialogManagerStub : IOverlayDialogManager
        {
            public IOverlayDialogController GetOverlayDialog(Transform targetTransform)
            {
                return null;
            }

            public void ToggleOverlayDialog(OverlayDialogConfig overlayDialogConfig)
            {
                
            }

            public void ShowOverlayDialog(OverlayDialogConfig overlayDialogConfig)
            {
                
            }

            public void HideAllOverlayDialogs()
            {
               
            }
        }
        
        private class TutorialPlaybackControllerStub : ITutorialPlaybackController
        {
            public void InitializeByContext(IContext context)
            {
                
            }

            public bool IsLevelBusyWithTutorial(string levelUid, int stage)
            {
                return false;
            }

            public bool TryToPlayFlowTutorial(out IEnumerator tutorialCoroutine)
            {
                tutorialCoroutine = null;
                return false;
            }

            public void StartTutorial()
            {
                
            }

            public BoolReactiveProperty IsTutorialShouldBlockNonImmediatePopupsDisplay { get; } = new();
            public BoolReactiveProperty IsTutorialShouldBlockMapScroll { get; } = new();
            public BoolReactiveProperty IsTutorialShouldForceShowHUD { get; } = new();
            public BoolReactiveProperty ShouldBeHiddenBySideOverlay { get; } = new();
            public IReadOnlyDictionary<string, string> TutorialLevelUidByBooster => new Dictionary<string, string>();
        }

        private class RoyaleEventManagerStub : IRoyaleEventManager
        {
            public IEnumerable<RoyaleEvent> GetAllEvents()
            {
                return new List<RoyaleEvent>();
            }

            public RoyaleEvent GetHighestPriorityEvent()
            {
                return null;
            }

            public RoyaleEvent GetRoyaleEvent(string eventUid)
            {
                return null;
            }

            public bool ShowRoyaleEvent(RoyaleEvent royaleEvent, bool shouldProcess = true)
            {
                throw new NotImplementedException();
            }

            public bool ShowRoyaleEvent(RoyaleEvent royaleEvent, ShowMode showMode = ShowMode.Delayed,
                bool shouldProcess = true)
            {
                return false;
            }

            public void ClaimRewardFor(RoyaleEvent royaleEvent)
            {
            }

            public void EnterLevelFlow()
            {
            }

            public bool TryAutoShowEvent(EventAutoshowCondition condition, Action closeCallback = null)
            {
                return false;
            }

            public bool ShouldAutoShow(EventAutoshowCondition condition)
            {
                return false;
            }

            public void ProcessEventStates()
            {
            }

            public void SuppressStatePenalty(bool suppress)
            {
            }
        }

        private class TeamEventManagerStub : ITeamEventManager
        {
            public event Action<TeamEvent> TeamEventReleased;

            public IEnumerable<TeamEvent> Events
            {
                get
                {
                    yield break;
                }
            }

            public IEnumerable<(string eventUid, int score)> GetLastDeltaScoreTuples()
            {
                yield break;
            }

            public void ClearLastDeltaScores()
            {
            }

            public TeamEvent GetHighestPriorityEvent()
            {
                return null;
            }

            public TeamEvent GetEvent(string eventUid)
            {
                return null;
            }

            public void ProcessEventStates()
            {
                
            }

            public bool TryToShowEvent(TeamEvent ev, bool shouldProcess)
            {
                throw new NotImplementedException();
            }

            public void IncrementScores(string eventUid, int score)
            {
                
            }

            public void DebugReleaseEvent(string eventUid)
            {
               
            }

            public void DebugSetScore(string eventUid, int score)
            {
                
            }

            public void TryCollectTeamCoopRewards(TeamEvent teamEvent, int milestoneArrayIndexToClaim)
            {
                
            }

            public void TryCollectTeamVsTeamRewards(TeamEvent teamEvent)
            {
                
            }

            public void ClaimContributionReward(TeamEvent teamEvent)
            {
                
            }

            public bool ShouldAutoShow(EventAutoshowCondition condition)
            {
                return false;
            }

            public bool TryAutoShowEvent(EventAutoshowCondition condition)
            {
                return false;
            }

            public void EnterLevelFlow()
            {
                
            }

            public void ResetTeamEvents()
            {
                
            }

            public void ShowIntroModal(TeamEvent teamEvent, bool infoMode, ShowMode showMode)
            {
                throw new NotImplementedException();
            }
        }

        private class RaceEventManagerStub : IRaceEventManager
        {
            public void ProcessEventStates()
            {
            }

            public void TryFetchDataSilently()
            {
            }

            public RaceEvent GetRaceEvent(string eventUid)
            {
                return null;
            }

            public RaceEvent GetHighestPriorityEvent()
            {
                return null;
            }

            public IEnumerable<RaceEvent> GetAllEvents()
            {
                return null;
            }

            public bool UpdateRemoteEventsData()
            {
                return false;
            }

            public bool TryToShowRace(RaceEvent raceEvent, ShowMode showMode,
                RaceInfoOpeningReason raceInfoOpeningReason, int ownPreviousRank = -1)
            {
                return false;
            }

            public void ShowRaceInfo(RaceEvent raceEvent, Action onHide)
            {
            }

            public void ClaimRewardFor(RaceEvent raceEvent)
            {
            }

            public void HandleLoss(RaceEvent raceEvent)
            {
            }

            public void IncrementScores(string eventUid, int score)
            {
            }

            public void DebugSetScore(string eventUid, int score)
            {
            }

            public void DebugReleaseEvent(string eventUid)
            {
            }

            public IEnumerable<(string raceEventUid, string stageUid, int score)> GetLastDeltaScoreTuples()
            {
                return null;
            }

            public void ClearLastDeltaScores()
            {
            }

            public bool CanShowHudIcon()
            {
                return true;
            }

            public IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)>
                GetFutureNotifiableEvents(Func<RaceEvent, DateTime> timeSelector)
            {
                return null;
            }

            public void FetchRemoteData()
            {
            }

            public void EnterLevelFlow()
            {
            }

            public void CollectEventInitialSetup(bool status, RaceEvent raceEvent, Action close)
            {
            }
        }

        private class GameEventManagerStub : IGameEventManager
        {
            public int TimeBeforeEventEndNotification { get; }
            public SweepStakesGameEventConfig SweepStakesGameEventConfig { get; }
            public event Action<GameEventBase> OnGameEventClaimedAfterAccomplish;
            public event Action<GameEventBase> OnGameEventExpired;
            public event Action<GameEventBase> OnGameEventLaunched;

            public IEnumerable<GameEventBase> GetEvents(Predicate<GameEventBase> filter)
            {
                return new List<GameEventBase>();
            }

            public bool CanLabelsBeGatheredAtLevel(string levelUid) => false;

            private GameEventBase _debugTestGameEvent;

            public GameEventManagerStub()
            {
                var config = new GameEventConfig()
                {
                    Uid = "m3_debug_event1",
                    AbsoluteStartTime = new DayMonthYear()
                    {
                        Year = DateTime.Now.Year, Day = Mathf.Max(1, DateTime.Now.Day - 1), Month = DateTime.Now.Month
                    },
                    DurationInDays = 10,
                    EventScoreGoal = 500,
                    GameplayType = (int)GameEventGameplayType.Completion,
                    SchedulingType = SchedulingType.Absolute.ToString(),
                };
                // _debugTestGameEvent = new CompletionGameEvent(config, null, null, null, null, null);
            }

            public void ClaimRewardsFor(GameEventBase gameEvent)
            {
            }

            public IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(
                Func<GameEventBase, DateTime> timeSelector)
            {
                return null;
            }

            public bool IsActiveSideMap(ScreenType screenType)
            {
                return false;
            }

            public GameEventBase GetHighestPriorityEventByStatus(GameEventStatus status)
            {
                return _debugTestGameEvent;
            }

            public GameEventBase GetHighestPriorityEvent(Predicate<GameEventBase> eventPredicate, bool skipResourceCheck = false)
            {
                return _debugTestGameEvent;
            }

            public GameEventMatch3LabelCounters GetMatch3DataFor(string levelUid) => null;

            public GameEventBase GetCurrentSideMapEvent()
            {
                return _debugTestGameEvent;
            }

            public GameEventBase GetAvailableSideMapEvent()
            {
                return _debugTestGameEvent;
            }

            public void IncrementScores(string eventUid, int score)
            {
            }

            public void ShowEvent(string eventUid, ShowMode showMode = ShowMode.Delayed)
            {
            }

            public void ShowCompletionModal(CompetitionGameEvent eventUid, ShowMode showMode = ShowMode.Delayed)
            {
            }

            public void ShowLeaderboardModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed)
            {
            }

            public void Process()
            {
            }

            public GameEventBase FindGameEventByUid(string gameEventUid) => null;

            public int GetLastScoreShown(string eventUid) => 0;

            public void SetLastScoreShown(string eventUid, int value)
            {
            }

            public void SetLastRankShown(string eventUid, int value)
            {
            }

            public void DebugSetScore(string eventUid, int score)
            {
            }

            public void DebugReleaseEvent(string eventUid)
            {
            }

            public void TryParticipateInLottery(string eventUid)
            {
            }

            public void ShowInformationModal(CompetitionGameEvent gameEvent, Action<bool> closeCallback)
            {
            }

            public void GoToNextGameEventLevel()
            {
            }

            public string GetOverrideGameEvent()
            {
                return "";
            }

            public void SetOverrideGameEvent(string value)
            {
            }

            public bool ShouldShowEndAnnouncement()
            {
                return false;
            }

            public bool TryAutoShowGameEvent()
            {
                return false;
            }

            public bool ShouldShowStartAnnouncement()
            {
                return true;
            }

            public bool ShouldShowQualificationAnnouncement()
            {
                return false;
            }

            public bool AnyFetchingData()
            {
                return false;
            }

            public int GetLastScoreSeenFor(string eventUid, string playerUid)
            {
                return -1;
            }

            public void SetLastScoreSeenFor(string eventUid, string playerUid, int value)
            {
            }

            public void ShowSweepstakesMilestoneModal(SweepstakesGameEvent sweepstakesGameEvent, ShowMode showMode = ShowMode.Delayed)
            {
            }

            public void ShowSweepstakesMilestoneRewardModal(SweepstakesGameEvent sweepstakesGameEvent,
                Transform rewardItem,
                Action onHide)
            {
            }

            public UniTask ShowSweepstakesGameEvent(SweepstakesGameEvent sweepstakesGameEvent, ShowMode showMode = ShowMode.Delayed)
            {
                return UniTask.CompletedTask;
            }

            public bool IsValidScore(string scoreId, out GameEventBase gameEvent)
            {
                gameEvent = null;
                return true;
            }
        }

        private class TeamCoopEventEventMatch3ManagerStub : ITeamCoopEventMatch3Manager
        {
            public void HandleDebugWin()
            {
                
            }

            public void ProcessOnLevelWin()
            {
                
            }

            public void Setup(ILevel level)
            {
                
            }

            public void ProcessOnExit(bool isStartedLevelPlay)
            {
                
            }

            public void ProcessOnLevelLose()
            {
                
            }

            public int EventScoreThatWillBeLost()
            {
                return 0;
            }
        }

        private class RoyaleEventMatch3ManagerStub : IRoyaleEventMatch3Manager
        {
            public bool ShouldActivateBackground { get; }

            public void HandleDebugWin()
            {
            }

            public void ProcessOnLevelWin()
            {
            }

            public void Setup(ILevel level)
            {
            }

            public void ProcessOnExit(bool isStartedLevelPlay)
            {
            }

            public void ProcessOnLevelLose()
            {
            }

            public bool MakeEventsPenalizable()
            {
                return false;
            }

            public int EventScoreThatWillBeLost()
            {
                return 0;
            }

            public void RestorePenalizableScore()
            {
            }
        }

        private class RaceEventMatch3ManagerStub : IRaceEventMatch3Manager
        {
            public void HandleDebugWin()
            {
            }

            public void ProcessOnLevelWin()
            {
            }

            public void Setup(ILevel level)
            {
            }

            public void ProcessOnExit(bool isStartedLevelPlay)
            {
            }
            
            public void ProcessOnShuffleFailed()
            {
            }

            public void ProcessOnLevelLose()
            {
            }

            public bool MakeEventsPenalizable()
            {
                return false;
            }

            public bool IsAnyEventStreakBroken()
            {
                return false;
            }

            public bool IsAnyRaceEventOfType(RaceEventTypes.RaceEventType type, out string eventUid)
            {
                //Makes Disco Rush work in Match 3 Editor
                eventUid = null;
                return true;
            }

            public void AddScore(int value, bool append = false)
            {
            }

            public int GetCollectEventScore()
            {
                return 0;
            }

            public string GetEventUid()
            {
                return string.Empty;
            }

            public void ProcessOnFirstMove()
            {
            }

            public bool IsAnyEventDoubleScoreActive()
            {
                return false;
            }

            public int GetHighestScoreMultiplier(bool includeCurrent)
            {
                return 1;
            }

            public IEnumerable<(GameEventBase gameEvent, DateTime dateTime)> GetAllFutureEvents(
                Func<GameEventBase, DateTime> timeSelector)
            {
                return null;
            }
        }

        private class GameEventMatch3ManagerStub : IGameEventMatch3Manager
        {
            public GameEventBase ActiveGameEvent => _debugTestGameEvent;

            public string ActiveGameEventUid => _debugTestGameEvent == null ? string.Empty : _debugTestGameEvent.Uid;

            public int SortOrder { get; }
            public bool IsNormalTileReskinGameEventType { get; }

            public TileKinds NormalTileSelectedForReskinOnThisLevel { get; set; }

            public int LastCollectedScores { get; }

            public int CollectedScores => currentCollectedScore;

            public int ScoresThatWouldBeLost => currentCollectedScore;

            public bool WillCompetitionProgressBeLost => false;
            public bool IsAlternateMapScreen => false;

            public bool IsPossibleToCollectGameEventScores => false;

            private int currentCollectedScore;

            private GameEventBase _debugTestGameEvent;

            public void Init(IContext context)
            {
                _debugTestGameEvent = context.Resolve<IGameEventManager>()
                    .GetHighestPriorityEvent(ev => ev.Status == GameEventStatus.Active);
            }

            public void Setup(ILevel level)
            {
                OnSetupEvent?.Invoke();
            }

            public void ProcessOnLevelWin()
            {
                currentCollectedScore = 0;
            }

            public void ProcessOnLevelLose()
            {
                currentCollectedScore = 0;
            }

            public void HandleDebugWin()
            {
                currentCollectedScore = 0;
            }

            public void AddGameEventScore(int score)
            {
                currentCollectedScore += score;
            }

            public GameEventGameplayType GetGameplayType()
            {
                return GameEventGameplayType.None;
            }

            public event Action OnSetupEvent;

            public void ProcessOnExit()
            {
                currentCollectedScore = 0;
            }

            public void ProcessOnShuffleFailed()
            {
            }

            public bool MakeScoresPenalizable()
            {
                return false;
            }

            public void ProcessOnFirstMove()
            {
            }

            public void SubmitScore(int score, bool append = false)
            {
            }

            public void FinalizeScore()
            {
            }
        }

        public class PlayerManagerStub : IPlayerManager
        {
            public PlayerManagerStub(List<FakeBooster> fakeBoosters)
            {
                var boostersDict = new Dictionary<string, int>();
                foreach (var booster in fakeBoosters.Where(fb => fb.active))
                    boostersDict.Add(booster.id, booster.count);
                PlayerInventory = new InventoryStub(new PBInventory
                {
                    Boosters = boostersDict
                });
            }

            public IPlayer Player { get; }
            public IInventory PlayerInventory { get; private set; }
            public ILevel CurrentLevel { get; private set; }
            private float _aimToWinSkillModifier;
            private float _aimToLoseSkillModifier;

            public UniTask<(bool, PlayerSavedGame)> TryToSave(bool force)
            {
                return UniTask.FromResult((false, default(PlayerSavedGame)));
            }

            public void SetCurrentLevel(ILevel level)
            {
                CurrentLevel = level;
            }

            public int StarCount { get; }
            public void AddStar(int amount = 1)
            {
            }

            public void SpendStar(int amount)
            {
            }

            public float GetOnAimToWinSkillModifier()
            {
                return _aimToWinSkillModifier;
            }

            public float GetOnAimToLoseSkillModifier()
            {
                return _aimToLoseSkillModifier;
            }

            public void SetOnAimToWinSkillModifier(float value)
            {
                _aimToWinSkillModifier = value;
            }

            public void SetOnAimToLoseSkillModifier(float value)
            {
                _aimToLoseSkillModifier = value;
            }

            public void OnLevelEnded(ILevel level, LevelOutcome levelOutcome, int movesLeft, int plus5MovesUsed,
                AssistParams assistParams)
            {
            }

            public void RecordIap(FBConfig.IAPStoreMarketItemConfig iapConfig)
            {
            }

            public void MarkDirty(bool force = false)
            {
                
            }

            public void TransactionMade(Transaction transaction)
            {
            }

            public bool IgnoreVisualTransactions { get; }
    	    public bool DisableSave { get; set; }
        }

        private class LevelRevealerStub : ILevelRevealer
        {
            public void AddObserver(ILevelRevealObserver observer)
            {
            }

            public void HideInstant()
            {
            }

            public void Hide(Action onDone)
            {
            }

            public void Reveal(Action onDone)
            {
            }

            public void HideBottomPanel()
            {
            }

            public void RevealBottomPanel()
            {
            }

            public void ResetCanvas()
            {
               
            }

            public void HideCanvas()
            {
              
            }
        }

        public class LevelSkipperStub : ILevelSkipper
        {
            public void ForbidToSkip()
            {
            }

            public void BlockSkipWithCallback(Action skipBlockedCallback)
            {
                throw new NotImplementedException();
            }

            public void BlockSkippingWithLocalizedText(string locKey)
            {
            }

            public void UnblockSkipping()
            {
            }

            public void InvokeSkipButton()
            {
            }

            public void TriggerTap()
            {
            }

            public void ShowSkipText()
            {
            }
        }

        private class EventsDispatcherStub : IEventDispatcher
        {
            public void Dispose()
            {
            }

            public void AddListener<TEvent>(Action<TEvent> handler, object sender) where TEvent : IEvent
            {
            }

            public void Register<TEvent>() where TEvent : IEvent, new()
            {
            }

            public TEvent GetMessage<TEvent>() where TEvent : IEvent
            {
                return default;
            }

            public void AddListener<TEvent>(Action<TEvent> handler) where TEvent : IEvent
            {
            }

            public void AddAsyncListener<TEvent>(Func<TEvent, UniTask> handler) where TEvent : IEvent
            {
            }

            public void RemoveListener<TEvent>(Action<TEvent> handler, object sender) where TEvent : class, IEvent
            {
            }

            public void RemoveListener<TEvent>(Action<TEvent> handler) where TEvent : class, IEvent
            {
            }

            public void RemoveAsyncListener<TEvent>(Func<TEvent, UniTask> handler) where TEvent : IEvent
            {
            }

            public void Unsubscribe(object instance)
            {
            }

            public void TriggerEvent<TEvent>(TEvent @event, object sender) where TEvent : class, IEvent
            {
            }

            public void TriggerEvent<TEvent>(TEvent @event) where TEvent : class, IEvent
            {
            }

            public UniTask TriggerEventAsync<TEvent>(TEvent @event) where TEvent : class, IEvent
            {
                return UniTask.CompletedTask;
            }

            public void TriggerEventNextFrame<TEvent>(TEvent @event) where TEvent : class, IEvent
            {
            }

            public void TriggerEventWithDelay<TEvent>(TEvent @event, float delay) where TEvent : class, IEvent
            {
            }

            public void TriggerEventWithDelay<TEvent>(TEvent @event, float delay, object sender) where TEvent : class, IEvent
            {
            }

            public void RemoveDelayedEvent(int id)
            {
            }

            public void CancelDelayedEvent<TEvent>(object sender) where TEvent : IEvent
            {
            }

            public void SetEventDelay(int id, float delay)
            {
            }

            public void ChangeDelayTime<TEvent>(object sender, int newRemainingTime)
            {
            }
        }

        private class ModalsBuilderStub : IModalsBuilder
        {
            public TController CreateView<TController>(ModalsType type) where TController : class, IController
            {
                return null;
            }

            public TController CreateView<TController>(ModalsType type, IContext context)
                where TController : class, IController
            {
                return null;
            }

            public void Hide<TController>(TController ctrl) where TController : IController
            {
            }

            public TController CreateModalView<TController>(ModalsType modalsType)
                where TController : class, IController, new()
            {
                return null;
            }

            public void Show<TController>(TController ctrl, string uid = null, ShowMode showMode = ShowMode.Immediate,
                ModalSetupParamsBase setupParams = null) where TController : IController
            {
            }

            public IController TryCreateModalView(ModalsType modalsType)
            {
                return null;
            }
        }

        private class InventoryStub : Inventory
        {
            public InventoryStub(PBInventory inventory) : base(inventory)
            {
            }

            public override IDictionary<string, int> GetBoosterCounts(IEnumerable<string> uids)
            {
                var dictionary = new Dictionary<string, int>();
                foreach (var key in PbInventory.Boosters.Keys)
                {
                    dictionary[key] = PbInventory.Boosters[key];
                }

                return dictionary;
            }

            public override int AddBooster(string uid, int amount)
            {
                int val;
                PbInventory.Boosters.TryGetValue(uid, out val);
                var newVal = val + amount;

                if (newVal < 0)
                {
                    BDebug.LogError(LogCat.Player, "Trying to set negative value to boosters count for " + uid);
                    return 0;
                }

                PbInventory.Boosters[uid] = newVal;

                return newVal;
            }
        }

        private class LockManagerStub : ILockManager
        {
            public bool IsMatch3LevelLockingType(string levelUid, LockItemType itemType)
            {
                return false;
            }

            public string GetLevelUidByItem(string uid, LockItemType itemType)
            {
                return "";
            }

            public void Init()
            {
                throw new NotImplementedException();
            }

            public bool IsLocked(string Id, LockItemType itemType)
            {
                return false;
            }

            public bool IsLocked(string Id, LockItemType itemType, out bool isExistConfig)
            {
                isExistConfig = false;
                return false;
            }

            public string GetLocationUidOfLockingCondition(string uid, LockItemType itemType)
            {
                throw new NotImplementedException();
            }

            public FBConfig.LockItemConfig GetLockReasonFor(string uid, LockItemType type)
            {
                throw new NotImplementedException();
            }

            public string GetFirstArg(FBConfig.LockItemConfig item)
            {
                throw new NotImplementedException();
            }

            public string LockedReasonText(string uid, LockItemType itemType,
                LockReasonTextType lockReasonTextType = LockReasonTextType.Long)
            {
                throw new NotImplementedException();
            }

            public FBConfig.LockItemConfig GetLockingConditionByType(string uid, LockItemType itemType,
                LockConditionType conditionType)
            {
                throw new NotImplementedException();
            }

            public bool IsLevelMarkerUILocked()
            {
                return false;
            }

            public bool IsMapHudLocked()
            {
                return false;
            }
        }

        private class StubLevelScoreAcceptor : ILevelScoreAcceptor
        {
            public void TryUpdateOwnScore(string configUid, int score)
            {
            }
        }
        
        private class StubScreensManager : IScreensManager, IContextInitializable
        {
            private readonly Transform _levelHolder;
            private IAssetsManager _assetsManager;

            public ScreenType GetTransitionTargetScreenType() => ScreenType.None;

            public int GetVisitsCountPerSession(ScreenType screen)
            {
                return 0;
            }

            public bool IsTransitionInProgress => false;

            public void RequestRestart()
            {
            }

            public float TransitionProgress { get; }

            public string GetTrackingPreviousScreenType()
            {
                return "";
            }

            public void EnforceTransition()
            {
            }

            public StubScreensManager(Transform levelHolder)
            {
                _levelHolder = levelHolder;
            }

            public StubScreensManager()
            {
            }

            public void InitializeByContext(IContext context)
            {
                _assetsManager = context.Resolve<IAssetsManager>();
            }

            public UniTask<TViewPresenter> PreloadView<TViewPresenter, TViewType>(TViewType type)
                where TViewPresenter : class, IViewPresenter
            {
                return UniTask.FromResult<TViewPresenter>(null);
            }

            public event Action<ScreenType, IScreensController> OnScreenChangingStarted;
            public event Action<ScreenType, IScreensController> ScreenCreated;
            public event Action<ScreenType> OnScreenTransitionComplete;
            public event Action<ScreenType> OnCurrentScreenTransitionComplete;
            public event Action<ScreenType, IScreensController, IViewPresenter> OnScreenChanged;
            public event Action<ScreenType, IScreensController> NextScreenPreShown = delegate { };
            public event Action<ScreenType> OnFailedScreenTransition;
            public event Action<ScreenType> OnSuccessedScreenTransition;

            public IViewPresenter TryGetCachedView<TController, TViewPresenter, TViewType>(TViewType type,
                TController ctrl)
                where TController : class, IController<TViewPresenter>, new()
                where TViewPresenter : class, IViewPresenter
                where TViewType : struct, IConvertible
            {
                return null;
            }

            public async UniTask<TViewPresenter> PreloadView<TController, TViewPresenter, TViewType>(TViewType type,
                TController ctrl) where TController : class, IController<TViewPresenter>, new()
                where TViewPresenter : class, IViewPresenter
                where TViewType : struct, IConvertible
            {
                return null;
            }

            public bool HasCachedController<TController, TViewPresenter, TViewType>(TViewType type)
                where TController : class, IController where TViewPresenter : class, IViewPresenter
            {
                return false;
            }

            public TController GetCachedController<TController, TViewPresenter, TViewType>(TViewType type)
                where TController : class, IController where TViewPresenter : class, IViewPresenter
            {
                return null;
            }

            public TViewPresenter CacheView<TController, TViewPresenter, TViewType>(TViewType type,
                TViewPresenter prefab, TController ctrl)
                where TController : class, IController<TViewPresenter>, new()
                where TViewPresenter : class, IViewPresenter, IViewGameObject
            {
                return null;
            }

            public TController GetOrCreateController<TController, TViewPresenter, TViewType>(One<TViewType> type)
                where TController : class, IController<TViewPresenter>, IController, new()
                where TViewPresenter : class, IViewPresenter
            {
                return null;
            }
            
            public UniTask<GameObject> PreloadGameObject<TController, TViewPresenter, TViewType>(TViewType type,
                TController ctrl) where TController : class, IController<TViewPresenter>, new()
                where TViewPresenter : class, IViewPresenter
                where TViewType : struct, IConvertible
            {
                return UniTask.FromResult<GameObject>(null);
            }

            public ScreenType ShowScreen(ScreenType uid, CommandBase transitionCommands,
                CreateScreenCommand createScreenCommand)
            {
                return uid;
            }

            public void PushModal(IController controller)
            {
            }

            public void ShowPreviousScreen()
            {
            }

            public IScreensController GetCurrentController()
            {
                return null;
            }

            public ScreenType GetCurrentScreenType()
            {
                return ScreenType.None;
            }

            public ScreenType GetPreviousScreenType()
            {
                return ScreenType.None;
            }


            public void ShowScreen(GameObject prefab, Action<GameObject> initializer = null)
            {
                var screenPrefab = prefab.transform.Find("LevelScreen").gameObject;
                var go = Instantiate(screenPrefab, _levelHolder, false);
                initializer.SafeInvoke(go);
            }

            public Coroutine StartCoroutine(IEnumerator routine)
            {
                throw new NotImplementedException();
            }

            public void StopCoroutine(Coroutine routine)
            {
                throw new NotImplementedException();
            }

            public void HideScreen(IController controller)
            {
                throw new NotImplementedException();
            }
        }

        public class ThemeManagerStub : IThemeManager
        {
            public event Action OnThemeUnlocked;

            public T GetThemedObject<T>(ThemePlacementId placementId, string additionalFilter, string eventUid = "", bool forceEventTheme = false) where T : Object => null;

            public GameObject GetThemedTransition(string prefabName) => null;

            public void Initialize(IContext context)
            {
            }

            public void Setup(ILockManager lockManager, IGameEventManager gameEventManager)
            {
            }

            public Vector4 GetHSVAdjust(string eventUid = "", bool forceEventTheme = false) => Vector4.zero;

            public Vector4 GetHSVAdjust() => Vector4.zero;
            
            public void Restart()
            {
            }
        }

        public class LivesManagerStub : ILivesManager
        {
            public event Action<bool> LifeCountChangeEvent;
            public event Action<bool> LifeRefillScheduledEvent;
            public event Action<bool> LivesFullyRefilledEvent;
            public int MaxLives { get; set; } = 5;
            public int MaxLivesWithCap { get; }
            public bool LifeLifterApplied { get; set; }
            public int LossStreak { get; }

            public void Init(IPlayerManager playerManager, IWalletManager wallet, IEventDispatcher dispatcher,
                IConfig config, ITimeController timeManager)
            {
                throw new NotImplementedException();
            }

            public void SetUIWalletManager(IUIWalletManager uiWalletManager)
            {
            }

            public void ValidateLives()
            {
            }


            public void Init()
            {
                throw new NotImplementedException();
            }

            public void AddLife(LivesData livesData)
            {
            }

            public void SpendLife(bool hidden = false)
            {
            }

            public bool IsInfiniteLivesActive => false;

            public bool CanPlayLevel => true;

            public long TimeTillLifeRefill => 0;

            public long TimeTillInfiniteLivesEnd => 0;
            public long TimeTillAllLivesRefill => 0;

            public bool HasMaxLives => true;

            public int NumberOfLives => 5;

            public void ResetLossStreak()
            {
            }

            public bool CanShowBoosterOffer() => false;

            public void AddLossToStreak()
            {
            }

            public bool HasNonZeroLossStreak() => false;

            public void OnAppGoToBackground()
            {
            }
        }

        public class BoosterManagerStub : IBoosterManager
        {
            public bool GameStarted
            {
                get => throw new NotImplementedException();
                set => throw new NotImplementedException();
            }

            public event Action<string> InfiniteBoosterStateChangeEvent;

            public void EquipInfiniteBooster(string boosterUid)
            {
            }

            public long GetMinutesLeft(string booster)
            {
                return 0;
            }

            public TimeSpan GetTimeLeft(string booster)
            {
                return new TimeSpan();
            }

            public void Init(IPlayerManager playerManager, IWalletManager wallet, IEventDispatcher dispatcher,
                IConfig config, ITimeController timeManager)
            {
            }

            public bool IsInfiniteBoosterActive(string booster)
            {
                return false;
            }

            public void OnLevelEndCleanup()
            {
            }

            public void RestoreEquippedAutoBooster(string appliedAutoBooster)
            {
            }

            public void SetInfiniteBoosterTimePeriod(string booster, int minutes, bool allowNegative)
            {
            }

            public void SpendEquippedAutoBoosters(string appliedAutoBooster)
            {
            }

            public bool StartedLevelWithBoosters(IEnumerable<string> boosterIds)
            {
                return false;
            }
        }

        public class GameEventResourceManagerStub : IGameEventResourceManager
        {
            public T GetGenericAsset<T>(string eventUid, string key) where T : Object => null;

            public Sprite GetSprite(string eventUid, string key) => null;
            public UniTask<Sprite> GetSpriteAsync(string eventUid, string key) => new(null);

            public UniTask SetupGameEvents(IGameEventProvider gameEventProvider, IEnumerable<GameEventBase> gameEvents)
            {
                return UniTask.CompletedTask;
            }

            public void SetupRaceEvents(IGameEventProvider gameEventProvider, List<RaceEvent> eventsList)
            {
            }

            public void SetupRoyaleEvents(IGameEventProvider gameEventProvider, List<RoyaleEvent> eventsList)
            {
            }

            public void SetupTeamCoopEvents(IGameEventProvider gameEventProvider, List<TeamEvent> eventsList)
            {
                
            }

            public GameEventSettings GetSettings(string eventUid)
            {
                return null;
            }

            public void Clear()
            {
            }

            public bool AreEventBundlesLoadedFor(string eventUid)
            {
                return true;
            }

            public bool AllAssetsAvailable(string eventUid)
            {
                return true;
            }

            public HashSet<string> GetBundleNamesForEvent(string eventUid, string locationUid = null)
            {
                return null;
            }

            public UniTask TryReloadPackAsync(string resourceId, ScreenType screenType, Action<bool> callback = null)
            {
                return UniTask.CompletedTask;
            }
        }

        public class OrientationTrackerStub : IOrientationTracker
        {
            public event Action<DeviceOrientation> OrientationChanged;

            public DeviceOrientation GetCurrentDeviceOrientation()
            {
                return DeviceOrientation.Portrait;
            }

            public float GetAngleOfRotationFromTo(DeviceOrientation @from, DeviceOrientation to)
            {
                return 0f;
            }

            public bool IsRotationFlip(DeviceOrientation @from, DeviceOrientation to)
            {
                return false;
            }

            public Vector2 GetOrientationRotatedVector(Vector2 @from, bool inverseLandscape = false)
            {
                return from;
            }
        }

        public class ChallengeTriviaManagerStub : ChallengeTriviaManager
        {
            public override void InitializeByContext(IContext context)
            {
            }

            public override void ReleaseByContext(IContext context)
            {
            }

            public override bool IsEnvironmentReady()
            {
                return false;
            }
        }

        public sealed class ButlerGiftManagerEditorStub : IButlerGiftManager
        {
            public int CurrentStreak => Mathf.Min(_cachedCurrentStreak, MaxStreak);
            public int InitialStreak { get; set; }
            public bool WillStreakBreak => false;
            public bool ShowGlobeIntro => false;

            public List<string> CurrentButlerGifts
            {
                get
                {
                    var gifts = GetButlerGiftBooster(CurrentStreak);
                    if (!gifts.IsNull()) return FlatBufferHelper.ToDict(gifts.Gifts, gifts.GiftsLength).Keys.ToList();
                    BDebug.LogError(LogCat.General, "Fennec's Perks gift is NULL");
                    return null;
                }
            }

            public int MaxStreak => _config.Get<ButlerGiftConfig>().Keys.Count;

            public bool IsPartOfDailyEvent { get; set; }

            private IConfig _config;
            private IPlayerManager _playerManager;
            private IInventory _inventory;
            private int _cachedCurrentStreak;

            public void Init(IContext context)
            {
                _config = context.Resolve<IConfig>();
                _playerManager = context.Resolve<IPlayerManager>();
                _inventory = _playerManager.PlayerInventory;
                _cachedCurrentStreak = 0;
            }

            public void SetCurrentStreak(int streak)
            {
                ResetAllBoosters();
                _cachedCurrentStreak = streak;
                OnLevelStarted();
            }

            private void ResetAllBoosters()
            {
                var gifts = GetButlerGiftBooster(CurrentStreak);
                if (gifts.IsNull()) return;
                var giftsDict = FlatBufferHelper.ToDict(gifts.Gifts, gifts.GiftsLength);
                foreach (var key in giftsDict.Keys) _inventory.UnequipAutoBooster(key);
            }

            private void OnLevelStarted()
            {
                if (!IsUnlockedAndAvailable()) return;
                if (CurrentStreak < 1) return;
                var gifts = GetButlerGiftBooster(CurrentStreak);
                if (gifts.IsNull()) return;
                var giftsDict = FlatBufferHelper.ToDict(gifts.Gifts, gifts.GiftsLength);
                AddCustomBooster(giftsDict);
            }

            private ButlerGiftConfig GetButlerGiftBooster(int currentWinStreak)
            {
                return _config.Get<ButlerGiftConfig>().TryGetValue(currentWinStreak.ToString(), out var butlerGift)
                    ? butlerGift
                    : default;
            }

            private void AddCustomBooster(Dictionary<string, int> gifts)
            {
                if (gifts == null) return;
                //Handle Extra Moves before adding to Player Inventory
                //Potential to cause issue with older extra moves
                if (gifts.TryGetValue(InventoryBoosters.ExtraMovesButler, out var gift))
                {
                    // _movesValue = gift;
                    // _movesAvailable = true;
                    gifts.Remove(InventoryBoosters.ExtraMovesButler);
                }

                //Add all other pre game boosters to Equipped Boosters list
                foreach (var kvp in gifts)
                {
                    _inventory.EquipInfiniteAutoBooster(kvp.Key);
                }
            }
            
            public void ProcessOnShuffleFailed()
            {
            }

            public void ProcessOnFirstMove()
            {
            }

            public void IncrementStreak()
            {
            }

            public void ResetStreak()
            {
            }

            public bool IsUnlockedAndAvailable()
            {
                return true;
            }

            public bool IsButlerGift(string boosterId)
            {
                return true;
            }

            public void ShowInfoModal(ShowMode showMode)
            {
            }
        }
    }
}
#endif