using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.RaceEvents;
using UnityEngine;

namespace BBB
{
    public class GameNotificationManager
    {
        private readonly Dictionary<NotifierType, INotifierStatus> _notifierByType = new();
        private readonly Dictionary<string, BaseNotifier> _namedNotifiers = new();
        private BadgeQuestCompletionNotifier _badgeQuestCompletionNotifier;

        private readonly Dictionary<string, BaseNotifier> _raceEventNotifiers = new();
        private readonly Dictionary<string, BaseNotifier> _royaleEventNotifiers = new();
        private readonly Dictionary<string, BaseNotifier> _teamCoopEventNotifiers = new();
        private readonly List<BaseNotifier> _allBaseNotifiers = new();

        private BaseNotifierAggregator _menuNotificationsAggregator;

        private BaseNotifier _facebookNotifier;
        private BaseNotifier _instagramNotifier;

        private BaseNotifierAggregator _storeAggregator;
        private BaseNotifierAggregator _featuredStoreAggregator;
        private BaseNotifierAggregator _coinsStoreAggregator;

        private BaseNotifier _dailyTriviaNotifier;
        private BaseNotifier _basketNotifier;

        private BaseNotifier _dailyLoginNotifier;

        private BaseNotifier _triviaChellangeNotifier;

        private BaseNotifier _chatNotifier;
        private BaseNotifier _iceBreakerNotifier;
        private BaseNotifier _settingsNotifier;

        private BaseNotifier _collectionNotifier;
        private BaseNotifier _storyNotifier;
        private BaseNotifier _scenesNotifier;

        private HelpDeskNotifier _helpDeskNotifier;

        private BaseNotifierAggregator _passportNotifier;

        private BaseNotifierAggregator _tripstagramNotifier;

        private BaseNotifierAggregator _allLeaderboardsNotifier;
        private BaseNotifierAggregator _teamsLeaderboardsNotifier;
        private BaseNotifierAggregator _playerLeaderboardsNotifier;
        private BaseNotifierAggregator _weeklyLeaderboardsNotifier;

        private LeaderboardNotifier _teamsWorldLeaderboardNotifier;
        private LeaderboardNotifier _teamsCountryLeaderboardNotifier;
        private LeaderboardNotifier _playersWorldLeaderboardNotifier;
        private LeaderboardNotifier _playersCountryLeaderboardNotifier;
        private LeaderboardNotifier _weeklyLeaderboardNotifier;
        private BaseNotifier _dailyTaskNotifier;

        public Action OnInitialized;

        public bool IsInitialized { get; private set; }

        public void Init(IContext context)
        {
            _notifierByType.Clear();

            _allBaseNotifiers.Clear();
            _raceEventNotifiers.Clear();

            _badgeQuestCompletionNotifier = new BadgeQuestCompletionNotifier();
            _badgeQuestCompletionNotifier.Init(context);
            _allBaseNotifiers.Add(_badgeQuestCompletionNotifier);
            var raceEventManager = context.Resolve<IRaceEventManager>();
            foreach (var raceEvent in raceEventManager.GetAllEvents())
                _raceEventNotifiers.TryAdd(raceEvent.Uid, new BaseNotifier());

            var royaleEventManager = context.Resolve<IRoyaleEventManager>();
            foreach (var royaleEvent in royaleEventManager.GetAllEvents())
                _royaleEventNotifiers.TryAdd(royaleEvent.Uid, new BaseNotifier());

            _passportNotifier = new BaseNotifierAggregator();
            _passportNotifier.AddDependency(_badgeQuestCompletionNotifier);
            _tripstagramNotifier = new BaseNotifierAggregator();

            _allLeaderboardsNotifier = new BaseNotifierAggregator();
            _teamsLeaderboardsNotifier = new BaseNotifierAggregator();
            _playerLeaderboardsNotifier = new BaseNotifierAggregator();
            _weeklyLeaderboardsNotifier = new BaseNotifierAggregator();
            _collectionNotifier = new BaseNotifier();
            _storyNotifier = new BaseNotifier();
            _scenesNotifier = new BaseNotifier();

            _allLeaderboardsNotifier.AddDependency(_teamsLeaderboardsNotifier);
            _allLeaderboardsNotifier.AddDependency(_playerLeaderboardsNotifier);
            _allLeaderboardsNotifier.AddDependency(_weeklyLeaderboardsNotifier);

            _teamsWorldLeaderboardNotifier = new LeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsWorld);
            _teamsWorldLeaderboardNotifier.Init(context);
            _allBaseNotifiers.Add(_teamsWorldLeaderboardNotifier);
            _teamsCountryLeaderboardNotifier = new LeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsCountry);
            _teamsCountryLeaderboardNotifier.Init(context);
            _allBaseNotifiers.Add(_teamsCountryLeaderboardNotifier);

            _teamsLeaderboardsNotifier.AddDependency(_teamsWorldLeaderboardNotifier);
            _teamsLeaderboardsNotifier.AddDependency(_teamsCountryLeaderboardNotifier);

            _playersWorldLeaderboardNotifier = new LeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersWorld);
            _playersWorldLeaderboardNotifier.Init(context);
            _allBaseNotifiers.Add(_playersWorldLeaderboardNotifier);
            _playersCountryLeaderboardNotifier = new LeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersCountry);
            _playersCountryLeaderboardNotifier.Init(context);
            _allBaseNotifiers.Add(_playersCountryLeaderboardNotifier);

            _playerLeaderboardsNotifier.AddDependency(_playersWorldLeaderboardNotifier);
            _playerLeaderboardsNotifier.AddDependency(_playersCountryLeaderboardNotifier);

            _weeklyLeaderboardNotifier = new LeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersWeekly);
            _weeklyLeaderboardNotifier.Init(context);
            _allBaseNotifiers.Add(_weeklyLeaderboardNotifier);

            _weeklyLeaderboardsNotifier.AddDependency(_weeklyLeaderboardNotifier);
            
            _notifierByType[NotifierType.PassportNotifier] = _passportNotifier;
            _notifierByType[NotifierType.TripstagramNotifier] = _tripstagramNotifier;
            _notifierByType[NotifierType.AllLeaderboardsNotifier] = _allLeaderboardsNotifier;
            _notifierByType[NotifierType.TeamsLeaderboardsNotifier] = _teamsLeaderboardsNotifier;
            _notifierByType[NotifierType.PlayerLeaderboardsNotifier] = _playerLeaderboardsNotifier;
            _notifierByType[NotifierType.WeeklyLeaderboardsNotifier] = _weeklyLeaderboardsNotifier;
            _notifierByType[NotifierType.CollectionNotifier] = _collectionNotifier;
            _notifierByType[NotifierType.StoryNotifier] = _storyNotifier;
            _notifierByType[NotifierType.ScenesNotifier] = _scenesNotifier;

            _dailyTriviaNotifier = new BaseNotifier();
            _dailyTaskNotifier = new BaseNotifier();
            _basketNotifier = new BaseNotifier();

            _dailyLoginNotifier = new BaseNotifier();

            _storeAggregator = new BaseNotifierAggregator();
            _storeAggregator.AddDependency(_dailyTriviaNotifier);
            _storeAggregator.AddDependency(_basketNotifier);

            _featuredStoreAggregator = new BaseNotifierAggregator();
            _featuredStoreAggregator.AddDependency(_basketNotifier);

            _coinsStoreAggregator = new BaseNotifierAggregator();
            _coinsStoreAggregator.AddDependency(_dailyTriviaNotifier);

            _notifierByType[NotifierType.Store] = _storeAggregator;
            _notifierByType[NotifierType.FeaturedStore] = _featuredStoreAggregator;
            _notifierByType[NotifierType.CoinsStore] = _coinsStoreAggregator;

            _facebookNotifier = new BaseNotifier();
            _instagramNotifier = new BaseNotifier();
            _triviaChellangeNotifier = new BaseNotifier();
            _helpDeskNotifier = new HelpDeskNotifier();
            _helpDeskNotifier.Init(context).Forget();
            _settingsNotifier = new BaseNotifier();

            _notifierByType[NotifierType.FacebookNotifier] = _facebookNotifier;
            _notifierByType[NotifierType.InstagramNotifier] = _instagramNotifier;
            _notifierByType[NotifierType.HelpDeskNotifier] = _helpDeskNotifier;
            _notifierByType[NotifierType.TriviaChallengeNotifier] = _triviaChellangeNotifier;
            _notifierByType[NotifierType.SettingsNotifier] = _settingsNotifier;
            _notifierByType[NotifierType.DailyTasksNotifier] = _dailyTaskNotifier;
            _notifierByType[NotifierType.DailyLoginNotifier] = _dailyLoginNotifier;
            _namedNotifiers["Settings"] = _settingsNotifier;

            _menuNotificationsAggregator = new BaseNotifierAggregator();
            _menuNotificationsAggregator.AddDependency(_passportNotifier);
            _menuNotificationsAggregator.AddDependency(_tripstagramNotifier);
            _menuNotificationsAggregator.AddDependency(_helpDeskNotifier);
            _menuNotificationsAggregator.AddDependency(_triviaChellangeNotifier);
            _menuNotificationsAggregator.AddDependency(_settingsNotifier);
            _menuNotificationsAggregator.AddDependency(_storeAggregator);
            _notifierByType[NotifierType.HudMenu] = _menuNotificationsAggregator;

            _chatNotifier = new BaseNotifier();
            _iceBreakerNotifier = new BaseNotifier();
            IsInitialized = true;
            OnInitialized.SafeInvoke();
        }

        public void DeInit()
        {
            foreach (var notifier in _allBaseNotifiers)
                notifier?.DeInit();

            _allBaseNotifiers.Clear();

            foreach (var notifier in _notifierByType)
            {
                notifier.Value?.ResetNotifiersActions();
            }
        }

        public INotifierStatus GetNotifierByType(NotifierType notifierType)
        {
            return _notifierByType.TryGetValue(notifierType, out var notifierStatus) ? notifierStatus : new BaseNotifier();
        }

        public BaseNotifier GetNamedNotifier(string name)
        {
            if (_namedNotifiers.TryGetValue(name, out var notifier))
                return notifier;

            Debug.LogError($"Couldn't find named notifier: {name} ");
            return new BaseNotifier();
        }

        public INotifier GetBadgeQuestNotifier()
        {
            return _badgeQuestCompletionNotifier;
        }

        public INotifier GetTriviaChallengeNotifier()
        {
            return _triviaChellangeNotifier;
        }

        public INotifier GetRaceEventNotifier(string raceEventUid)
        {
            if (_raceEventNotifiers.TryGetValue(raceEventUid, out var notifier))
                return notifier;

            var newNotifier = new BaseNotifier();
            _raceEventNotifiers[raceEventUid] = newNotifier;

            return newNotifier;
        }

        public INotifier GetRoyaleEventNotifier(string eventUid)
        {
            if (_royaleEventNotifiers.TryGetValue(eventUid, out var notifier))
                return notifier;

            var newNotifier = new BaseNotifier();
            _royaleEventNotifiers[eventUid] = newNotifier;

            return newNotifier;
        }

        public INotifier GetTeamCoopEventNotifier(string eventUid)
        {
            if (_teamCoopEventNotifiers.TryGetValue(eventUid, out var notifier))
                return notifier;

            var newNotifier = new BaseNotifier();
            _teamCoopEventNotifiers[eventUid] = newNotifier;

            return newNotifier;
        }

        public INotifier GetDailyTriviaNotifier()
        {
            return _dailyTriviaNotifier;
        }

        public INotifier GetDailyLoginNotifier()
        {
            return _dailyLoginNotifier;
        }

        public INotifier GetDailyTaskNotifier()
        {
            return _dailyTaskNotifier;
        }

        public INotifier GetBasketNotifier()
        {
            return _basketNotifier;
        }
        
        public INotifier GetHelpDeskNotifier()
        {
            return _helpDeskNotifier;
        }

        public BaseNotifier GetChatNotifier()
        {
            return _chatNotifier;
        }
        
        public BaseNotifier GetIceBreakerNotifier()
        {
            return _iceBreakerNotifier;
        }

        public BaseNotifier GetCollectionNotifier()
        {
            return _collectionNotifier;
        }

        public BaseNotifier GetStoryNotifier()
        {
            return _storyNotifier;
        }

        public BaseNotifier GetScenesNotifier()
        {
            return _scenesNotifier;
        }

        public INotifierStatus GetNotifierByStoreCategory(string categoryUid)
        {
            if (categoryUid == "featured")
                return _featuredStoreAggregator;

            if (categoryUid == "regular")
                return _coinsStoreAggregator;

            return null;
        }

        public INotifier GetLeaderboardNotifier(LeaderboardNotifier.NotifierType type)
        {
            switch (type)
            {
                case LeaderboardNotifier.NotifierType.TeamsWorld:
                    return _teamsWorldLeaderboardNotifier;
                case LeaderboardNotifier.NotifierType.TeamsCountry:
                    return _teamsCountryLeaderboardNotifier;
                case LeaderboardNotifier.NotifierType.PlayersWorld:
                    return _playersWorldLeaderboardNotifier;
                case LeaderboardNotifier.NotifierType.PlayersCountry:
                    return _playersCountryLeaderboardNotifier;
                case LeaderboardNotifier.NotifierType.PlayersWeekly:
                    return _weeklyLeaderboardNotifier;
                default:
                    throw new ArgumentOutOfRangeException(nameof(type), type, null);
            }
        }
    }
}