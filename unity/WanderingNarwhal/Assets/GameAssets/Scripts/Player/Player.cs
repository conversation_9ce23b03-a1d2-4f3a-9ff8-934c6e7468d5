using System.Collections.Generic;
using PBGame;
using BBB.Player;
using BBB;
using System;
using BBB.Core;
using BBB.Wallet;
using GameAssets.Scripts.Collection;
using GameAssets.Scripts.Player;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Map;
using PBConfig;
using TeamEventConfig = FBConfig.TeamEventConfig;

public class Player : IPlayer
{
    public event Action<string> ModalVisited = delegate { };

    public const int EconomyMultiplier = 10;

    private readonly PBPlayer _playerDo;
    private readonly IInventory _inventory;
    private readonly ISocialInfo _socialInfo;
    private readonly GameEventStateProxy _gameEventStateProxy;
    private readonly GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig> _raceEventStateProxy;
    private readonly GenericEventStateProxy<PBRoyaleEventState, RoyaleGameEventConfig> _royaleEventStateProxy;
    private readonly GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig> _teamCoopEventStateProxy;
    private readonly ChallengeTriviaStateProxy _challengeTriviaStateProxy;
    private readonly SdbStateProxy _sdbStateProxy;
    private readonly TeamDataCacheProxy _teamDataCacheProxy;

    public Player(PBPlayer player)
    {
        _playerDo = player;
        
        _playerDo.Locations ??= new Dictionary<string, PBLocation>();
        _playerDo.Wallet ??= new PBWallet();

        _playerDo.IAPHistory ??= new List<PBIAPRecord>();
        _playerDo.LevelStageUnlocked ??= new Dictionary<string, int>();
        _playerDo.WorldCity ??= new PBWorldCity();
        _playerDo.Gacha ??= new PBGacha();

        _playerDo.Inventory ??= new PBInventory();
        _inventory = new Inventory(_playerDo.Inventory);

        _playerDo.ObjectivesInProgress ??= new Dictionary<string, PBObjectiveProgress>();
        ObjectivesProgress = new ObjectivesProgress(_playerDo.ObjectivesInProgress);

        _playerDo.Placeables ??= new Dictionary<string, PBMapPlaceable>();
        _playerDo.TutorialPersistentData ??= new PBTutorialPersistentData();
        _playerDo.Stats ??= new PBStats();
        _playerDo.TutorialPersistentData.VisitedModals ??= new List<string>();
        _playerDo.TutorialPersistentData.StepsCompletedDict ??= new Dictionary<string, int>();

        _playerDo.QuestProgress ??= new Dictionary<string, PBQuestProgress>();
        _playerDo.ExpiredQuests ??= new List<string>();
        _playerDo.CompletedQuests ??= new List<string>();
        _playerDo.OrphanObjectivesInProgress ??= new List<PBObjectiveProgress>();
        _playerDo.POIEntityStates ??= new Dictionary<string, POIEntityState>();
        _playerDo.NotificationsDictionary ??= new Dictionary<string, int>();
        _playerDo.CurrentNuxStep ??= new List<string>();

        _playerDo.PromotionData ??= new PBPromotionData();
        _playerDo.PromotionData.PromotionsDisplayCount ??= new Dictionary<string, int>();
        _playerDo.PromotionData.PromotionsLastShownSessionNumber ??= new Dictionary<string, int>();
        _playerDo.PromotionData.PromotionsActivationTime ??= new Dictionary<string, double>();
        _playerDo.PromotionData.InteractedPromotions ??= new List<string>();

        _playerDo.PassportGameEventRecords ??= new List<PBPassportGameEventRecord>();

        PassportInfo = new PassportInfo(_playerDo.PassportGameEventRecords);
        _playerDo.SocialData ??= new SocialData()
        {
            LastSeenRankWorld = -1,
            LastSeenRankCountry = -1,
            LastSeenRankFriends = -1,
            LastVisualizedRankWorld = -1,
            LastVisualizedRankCountry = -1,
            LastVisualizedRankFriends = -1,
        };

        _playerDo.GameEventStates ??= new Dictionary<string, PBGameEventState>();

        _playerDo.GameEventPersistentState ??= new GameEventPersistentState();

        _playerDo.RaceEventStates ??= new Dictionary<string, PBRaceEventState>();

        _playerDo.RoyaleEventStates ??= new Dictionary<string, PBRoyaleEventState>();

        _playerDo.TeamEventStates ??= new Dictionary<string, PBTeamCoopEventState>();

        _socialInfo = new SocialInfo(_playerDo.SocialData);

        _gameEventStateProxy = new GameEventStateProxy(_playerDo.GameEventStates, _playerDo.GameEventPersistentState);
        _raceEventStateProxy = new GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig>(_playerDo.RaceEventStates);
        _royaleEventStateProxy = new GenericEventStateProxy<PBRoyaleEventState, RoyaleGameEventConfig>(_playerDo.RoyaleEventStates);
        var royalEventStates = _royaleEventStateProxy.States;
        if (royalEventStates != null)
        {
            foreach (var royaleEventState in royalEventStates)
            {
                royaleEventState.SpentByStep ??= new Dictionary<int, Dictionary<string, long>>();
            }
        }

        _teamCoopEventStateProxy = new GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig>(_playerDo.TeamEventStates);

        _playerDo.TeamDataCache ??= new TeamDataCacheState();
        _teamDataCacheProxy = new TeamDataCacheProxy(_playerDo.TeamDataCache);

        _playerDo.UsedDailyTriviaUids ??= new Dictionary<string, int>();

        POIRewardsInfo = new POIRewardsInfo(_playerDo);
        _playerDo.AnnouncementLastShowTimestamp ??= new Dictionary<string, PBAnnouncementShow>();

        var currentVersion = PlatformUtil.GetAppVersion();
        if (_playerDo.CurrentVersion != currentVersion)
        {
            _playerDo.PreviousVersion = !_playerDo.CurrentVersion.IsNullOrEmpty() ? _playerDo.CurrentVersion : currentVersion;

            _playerDo.CurrentVersion = currentVersion;
        }

        _playerDo.StealState ??= new PBStealState();
        
        _challengeTriviaStateProxy = new ChallengeTriviaStateProxy(_playerDo);
        _sdbStateProxy = new SdbStateProxy(_playerDo);
        
        if (_playerDo.ChallengeTriviaState != null && _playerDo.SdbState == null)
        {
            // First time after sdb/wiggles decoupling, we need to migrate SDB state from ChallengeTriviaState
            var oldSdbState = _playerDo.ChallengeTriviaState;
            _playerDo.SdbState = new SdbState
            {
                IntroductionAlreadyShown = oldSdbState.IntroductionAlreadyShown,
                SdbEnabled = oldSdbState.SdbEnabled,
                CurrentWinCount = oldSdbState.CurrentWinCount,
                LastShownScore = oldSdbState.CurrentWinCount
            };
            SdbMigrated = true;
        }
        else
        {
            _playerDo.SdbState ??= new SdbState();
        }
        
        _playerDo.ChallengeTriviaState ??= new ChallengeTriviaState();
        _playerDo.ChallengeTriviaState.LastSeenChallengeTime ??= new Dictionary<string, long>();
        _playerDo.ChallengeTriviaState.RemovedFavoritesToSync ??= new Dictionary<string, List<string>>();
        _playerDo.ChallengeTriviaState.AddedFavoritesToSync ??= new Dictionary<string, PBFriendData>();
        _playerDo.ChallengeTriviaState.ClaimedChallengesToSync ??= new Dictionary<string, PBChallengeInfo>();
        _playerDo.ChallengeTriviaState.DeclinedChallengesToSync ??= new Dictionary<string, PBChallengeInfo>();
        _playerDo.ChallengeTriviaState.NudgedChallengesToSync ??= new Dictionary<string, PBChallengeInfo>();
        _playerDo.ChallengeTriviaState.AnsweredChallengesToSync ??= new Dictionary<string, PBAnsweredChallengeInfo>();
        _playerDo.ChallengeTriviaState.ChallengeTriviaCache ??= new Dictionary<string, PBChallengeTriviaCache>();
        _playerDo.ChallengeTriviaState.AddedActiveSuggestionsToSync ??= new Dictionary<string, PBFriendData>();
        _playerDo.ChallengeTriviaState.ActiveSuggestionsEndedTimes ??= new Dictionary<string, long>();

        _playerDo.ClaimedLocations ??= new List<string>();
        _playerDo.NotValidatedVIPWonders ??= new List<string>();

        _playerDo.WeeklyLeaderboardState ??= new WeeklyLeaderboardState();

        _playerDo.ActiveAbTestGroups ??= new Dictionary<string, string>();

        _playerDo.EndlessTreasureProgress ??= new Dictionary<string, string>();

        _playerDo.DailyLoginData ??= new PBDailyLoginData
        {
            DailyTriviaSeed = _playerDo.DailyTriviaSeed
        };

        _playerDo.LevelOutcomeStreaks ??= new PBLevelOutcomeStreaks();

        _playerDo.ForcedActivePromotions ??= new Dictionary<string, PBPromotionActivationData>();

        _playerDo.LastRewardedMessageTimestamps ??= new List<double>();

        _playerDo.BattlePassProgress ??= new Dictionary<string, BattlePassProgress>();
        foreach (var battlePassUid in _playerDo.BattlePassProgress.Keys)
            _playerDo.BattlePassProgress[battlePassUid] ??= new BattlePassProgress();

        _playerDo.AnsweredSurveys ??= new List<string>();

        _playerDo.AdminGifts ??= new Dictionary<string, int>();
        _playerDo.LastAppliedGiftEntity ??= new Dictionary<string, double>();
        _playerDo.LastInteractionTimestamps ??= new Dictionary<string, double>();
        _playerDo.LevelStates ??= new List<LevelState>();
        _playerDo.RemovedLevelStatesIdsList ??= new List<string>();

        _playerDo.CollectionSets ??= new List<PBCollectionSet>();
        _playerDo.CollectionCards ??= new Dictionary<string, List<PBCollectionCard>>();
        _playerDo.CollectionCards.ReplaceNullsInListsDict();

        _playerDo.PendingToBeExecutedDeepLinks ??= new List<string>();
        _playerDo.PendingToBeConsumedDeepLinks ??= new List<string>();

        _playerDo.EpisodeScenesProgress ??= new Dictionary<string, PBEpisodeSceneProgress>();
        foreach (var (_, progress) in _playerDo.EpisodeScenesProgress)
        {
            progress.CompletedTasks ??= new List<string>();
        }

        _playerDo.OpenedEpisodeScenes ??= new List<string>();
        if (_playerDo.OpenedEpisodeScenes.Count == 0)
        {
            _playerDo.OpenedEpisodeScenes.Add(EpisodicScenesManager.DefaultEpisodicScene);
            _playerDo.CurrentEpisodeScene = EpisodicScenesManager.DefaultEpisodicScene;
        }

        _playerDo.LastTeamVsTeamNudges ??= new List<double>();

        _playerDo.DailyTaskState ??= new DailyTaskState();
        _playerDo.DailyTaskState.Tasks ??= new List<TaskState>();
        _playerDo.ChallengesStartedByPlayerUid ??= new Dictionary<string, int>();
        _playerDo.DiscardedChallengePlayerUids ??= new List<string>();
        _playerDo.ChallengeTriviaSeenTimes ??= new Dictionary<string, int>();
        
        _playerDo.DailyLoginClaimedDays ??= new List<int>();
    }

    public void UpdateEconomy()
    {
        if (!_playerDo.UpdatedEconomy)
        {
            // Bought currency is handled by the server!
            var regularCurrency = _playerDo.Wallet.WalletCurrency[WalletCurrencies.RegularCurrency];
            regularCurrency.Earned *= EconomyMultiplier;
            regularCurrency.Spent *= EconomyMultiplier;

            var premiumCurrency = _playerDo.Wallet.WalletCurrency[WalletCurrencies.PremiumCurrency];
            premiumCurrency.Earned *= EconomyMultiplier;
            premiumCurrency.Spent *= EconomyMultiplier;

            _playerDo.UpdatedEconomy = true;
        }
    }

    public PBPlayer PlayerDO
    {
        get { return _playerDo; }
    }

    public PBPromotionData PromotionData => _playerDo.PromotionData;

    public PBTutorialPersistentData TutorialPersistentData
    {
        get { return _playerDo.TutorialPersistentData; }
    }

    public long InfiniteLivesEndTimestamp
    {
        get { return _playerDo.InfiniteLifeEndTimestamp; }

        set { _playerDo.InfiniteLifeEndTimestamp = value; }
    }

    public IInventory Inventory
    {
        get { return _inventory; }
    }

    public int ButlerStreak
    {
        get => _playerDo.Stats.ButlerStreak;
        set => _playerDo.Stats.ButlerStreak = value;
    }

    public int ButlerWinStreak
    {
        get => _playerDo.Stats.ButlerWinStreak;
        set => _playerDo.Stats.ButlerWinStreak = value;
    }

    public int ButlerLongestWinStreak => _playerDo.Stats.ButlerLongestWinStreak;

    public List<PBCollectionSet> CollectionSets => _playerDo.CollectionSets;
    public IDictionary<string, List<PBCollectionCard>> CollectionCards => _playerDo.CollectionCards;

    public IDictionary<string, PBEpisodeSceneProgress> EpisodeScenesProgress => _playerDo.EpisodeScenesProgress;
    public string CurrentEpisodeScene
    {
        get => _playerDo.CurrentEpisodeScene;
        set => _playerDo.CurrentEpisodeScene = value;
    }

    public List<string> OpenedEpisodeScenes => _playerDo.OpenedEpisodeScenes;

    public int WildCardTokenAmount
    {
        get => _playerDo.WildCardTokenAmount;
        set => _playerDo.WildCardTokenAmount = value;
    }

    public int WildCardTokenAmountDelta
    {
        get => _playerDo.WildCardTokenAmountDelta;
        set => _playerDo.WildCardTokenAmountDelta = value;
    }

    public double SaveTime
    {
        get { return _playerDo.SaveTime; }
    }

    public ISocialInfo SocialInfo
    {
        get { return _socialInfo; }
    }

    public GameEventStateProxy GameEventStateProxy => _gameEventStateProxy;
    public GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig> RaceEventStateProxy => _raceEventStateProxy;
    public GenericEventStateProxy<PBRoyaleEventState, RoyaleGameEventConfig> RoyaleEventStateProxy => _royaleEventStateProxy;

    public GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig> TeamEventStateProxy => _teamCoopEventStateProxy;
    public ChallengeTriviaStateProxy ChallengeTriviaStateProxy => _challengeTriviaStateProxy;
    public SdbStateProxy SdbStateProxy => _sdbStateProxy;
    public TeamDataCacheProxy TeamDataCacheProxy => _teamDataCacheProxy;
    public bool SdbMigrated { get; }

    public double MaxIAPUSD
    {
        get { return _playerDo.MaxIAPUSD; }

        set { _playerDo.MaxIAPUSD = value; }
    }

    public string MaxIAPBought
    {
        get { return _playerDo.MaxIAPBought; }

        set { _playerDo.MaxIAPBought = value; }
    }

    public long NextLifeTimestamp
    {
        get { return _playerDo.NextLifeTimestamp; }

        set { _playerDo.NextLifeTimestamp = value; }
    }

    public double TotalSpendOnIAP
    {
        get { return _playerDo.TotalIAPUSD; }
    }

    public IDictionary<string, int> LevelStageUnlocked
    {
        get { return _playerDo.LevelStageUnlocked; }
    }

    public double InstallDate => _playerDo.InstallDate;

    public int MaxLives
    {
        get { return _playerDo.MaxLives; }

        set { _playerDo.MaxLives = value; }
    }

    public bool LifeLifterApplied
    {
        get { return _playerDo.LifeLifterApplied; }

        set { _playerDo.LifeLifterApplied = value; }
    }

    public bool IsPayer
    {
        get
        {
#if UNITY_EDITOR
            if (UnityEditor.EditorPrefs.GetBool("OverridePayerTrue", false))
            {
                return true;
            }
            else if (UnityEditor.EditorPrefs.GetBool("OverridePayerFalse", false))
            {
                return false;
            }
#endif

            return _playerDo.TotalIAPUSD > 0f;
        }
    }

    public bool IsWhale
    {
        get { return _playerDo.TotalIAPUSD >= 100f; }
    }

    public bool IsSuperWhale
    {
        get { return _playerDo.TotalIAPUSD >= 500f; }
    }

    public PBGacha Gacha
    {
        get { return PlayerDO.Gacha; }
    }

    public IObjectivesProgress ObjectivesProgress { get; private set; }

    public IDictionary<string, PBQuestProgress> QuestProgress
    {
        get { return _playerDo.QuestProgress; }
    }

    public IDictionary<string, PBMapPlaceable> Placeables
    {
        get { return _playerDo.Placeables; }
    }


    public List<string> ExpiredQuests
    {
        get { return _playerDo.ExpiredQuests; }
    }

    public List<string> CompletedQuests
    {
        get { return _playerDo.CompletedQuests; }
    }

    public void MarkAsVisited(ModalsType modalType)
    {
        string modalTypeToString = modalType.ToString();
        if (!_playerDo.TutorialPersistentData.VisitedModals.Contains(modalTypeToString))
        {
            _playerDo.TutorialPersistentData.VisitedModals.Add(modalTypeToString);
            ModalVisited(modalTypeToString);
        }
    }

    public void MarkAsVisited(string name)
    {
        if (name == null) return;

        if (!_playerDo.TutorialPersistentData.VisitedModals.Contains(name))
        {
            _playerDo.TutorialPersistentData.VisitedModals.Add(name);
            ModalVisited(name);
        }
    }

    public void CompleteTutorialStep(string step)
    {
        _playerDo.TutorialPersistentData.StepsCompletedDict[step] = 1;
    }

    public bool HasCompletedTutorialStep(string step)
    {
        return _playerDo.TutorialPersistentData.StepsCompletedDict.ContainsKey(step);
    }

    public PassportInfo PassportInfo { get; }

    public int GetNumberOfSessions()
    {
        return _playerDo.Stats.SessionsNumber;
    }

    public int LossStreak => _playerDo.Stats.GlobalLossStreak;
    public int WinStreak => _playerDo.Stats.CurrentWinStreak;
    public int FirstTryWinsCount => _playerDo.Stats.FirstTryWinsCount;

    public void ResetLossStreak()
    {
        _playerDo.Stats.GlobalLossStreak = 0;
    }

    public void IncLossStreak()
    {
        _playerDo.Stats.GlobalLossStreak++;
    }

    public int LongestWinStreak => _playerDo.Stats.LongestWinStreak;

    public void ResetWinStreak()
    {
        _playerDo.Stats.CurrentWinStreak = 0;
    }

    public void IncCurrentWinStreak()
    {
        _playerDo.Stats.CurrentWinStreak++;
        if (_playerDo.Stats.CurrentWinStreak > _playerDo.Stats.LongestWinStreak)
        {
            _playerDo.Stats.LongestWinStreak = _playerDo.Stats.CurrentWinStreak;
        }
    }

    public void TryIncrementFirstTryWinsCount()
    {
        if (_playerDo.Stats.GlobalLossStreak == 0)
        {
            _playerDo.Stats.FirstTryWinsCount++;
        }
    }

    public void ResetButlerWinStreak()
    {
        _playerDo.Stats.ButlerWinStreak = 0;
    }

    public void IncrementCurrentButlerWinStreak(int cachedWinStreak)
    {
        _playerDo.Stats.ButlerWinStreak = ++cachedWinStreak;
        if (ButlerWinStreak > ButlerLongestWinStreak)
        {
            _playerDo.Stats.ButlerLongestWinStreak = _playerDo.Stats.ButlerWinStreak;
        }
    }

    public bool IsVisited(string modalTypeName)
    {
        return _playerDo.TutorialPersistentData.VisitedModals.Contains(modalTypeName);
    }

    public List<PBObjectiveProgress> OrphanObjectivesInProgress
    {
        get { return _playerDo.OrphanObjectivesInProgress; }
    }

    public bool IsFBConnectRewardReceived
    {
        get { return PlayerDO.IsFBConnectRewardReceived; }

        set { PlayerDO.IsFBConnectRewardReceived = value; }
    }

    public PBWallet Wallet
    {
        get { return _playerDo.Wallet; }
    }

    public void AddIAPRecord(string uid, double priceUSD, double timestamp)
    {
        var record = new PBIAPRecord
        {
            Uid = uid,
            PriceUSD = priceUSD,
            Timestamp = timestamp,
        };

        _playerDo.IAPHistory.Add(record);
        _playerDo.TotalIAPUSD += priceUSD;
        if (priceUSD > MaxIAPUSD)
        {
            MaxIAPUSD = priceUSD;
            MaxIAPBought = uid;
        }
    }

    public string GetLastIAPBought()
    {
        string uid = "";
        if (_playerDo.IAPHistory.Count > 0)
        {
            PBIAPRecord record = _playerDo.IAPHistory[_playerDo.IAPHistory.Count - 1];
            uid = record.Uid;
        }

        return uid;
    }

    public double GetLastIAPPrice()
    {
        double price = 0D;
        if (_playerDo.IAPHistory.Count > 0)
        {
            PBIAPRecord record = _playerDo.IAPHistory[_playerDo.IAPHistory.Count - 1];
            price = record.PriceUSD;
        }

        return price;
    }

    public double GetLastTimeIAP()
    {
        double relative = 0;
        if (_playerDo.IAPHistory.Count > 0)
        {
            // PBIAPRecord record = gameDO.IAPHistory[gameDO.IAPHistory.Count - 1];
            // relative = timeManager.CurrentTimeStamp() - record.Timestamp;
        }

        return relative;
    }

    public bool HasPurchased(string iapConfigUid)
    {
        foreach (var x in _playerDo.IAPHistory)
        {
            if (x.Uid == iapConfigUid) return true;
        }

        return false;
    }

    public bool HasPurchasedAnyOf(string productFamilyUid)
    {
        foreach (var iapRecord in _playerDo.IAPHistory)
        {
            if (iapRecord.Uid.Contains(productFamilyUid))
                return true;
        }

        return false;
    }

    public void IncreaseNuxStages()
    {
        _playerDo.NuxCompleted++;
    }

    public void AddNuxStepCompleted(string name)
    {
        _playerDo.CurrentNuxStep.Add(name);
    }

    public bool IsNuxStepCompleted(string name)
    {
        return _playerDo.CurrentNuxStep.Contains(name);
    }

    public bool TryGetAdCooldownState(string key, out PlayerAdData adData)
    {
        if (_playerDo.AdData != null && _playerDo.AdData.TryGetValue(key, out var data))
        {
            adData = new PlayerAdData() { CooldownEndTime = data.CooldownEndTime, NumWatched = data.NumWatched };
            return true;
        }

        adData = default(PlayerAdData);
        return false;
    }

    public void SetAdCooldownState(string key, PlayerAdData adData)
    {
        _playerDo.AdData ??= new Dictionary<string, PBPlayerAdData>();

        _playerDo.AdData[key] = new PBPlayerAdData()
        {
            CooldownEndTime = adData.CooldownEndTime, NumWatched = adData.NumWatched
        };
    }

    public void AddWatchedAd()
    {
        _playerDo.Stats.AdsWatched++;
    }

    public int GetAdsWatched()
    {
        return _playerDo.Stats.AdsWatched;
    }

    public Dictionary<string, int> UsedDailyTriviaUids => _playerDo.UsedDailyTriviaUids;

    public int DaysSinceLastVisit { get; set; }

    public string CurrentVersion
    {
        get => _playerDo.CurrentVersion;
        set => _playerDo.CurrentVersion = value;
    }

    public string PreviousVersion
    {
        get => _playerDo.PreviousVersion;
        set => _playerDo.PreviousVersion = value;
    }

    public PBBasketData BasketData
    {
        get
        {
            if (_playerDo.BasketData == null)
                _playerDo.BasketData = new PBBasketData();

            if (_playerDo.BasketData.LastPurchaseTimestamp == null)
                _playerDo.BasketData.LastPurchaseTimestamp = new Dictionary<string, double>();

            if (_playerDo.BasketData.LastClaimTimestamp == null)
                _playerDo.BasketData.LastClaimTimestamp = new Dictionary<string, double>();

            return _playerDo.BasketData;
        }
    }

    public POIRewardsInfo POIRewardsInfo { get; }

    public List<string> NotValidatedVIPWonders => _playerDo.NotValidatedVIPWonders;
    public WeeklyLeaderboardState WeeklyLeaderboardState => _playerDo.WeeklyLeaderboardState;

    public bool ContainsAbTest(string abTest)
    {
        return _playerDo.ActiveAbTestGroups.ContainsKey(abTest);
    }

    public string GetActiveGroupFor(string abTest)
    {
        return _playerDo.ActiveAbTestGroups.ContainsKey(abTest) ? _playerDo.ActiveAbTestGroups[abTest] : null;
    }

    public bool BelongsToAbTestGroup(string abTest, string group)
    {
        return _playerDo.ActiveAbTestGroups.ContainsKey(abTest) && _playerDo.ActiveAbTestGroups[abTest] == group;
    }

    public void AddAbTestGroup(string abTest, string group)
    {
        _playerDo.ActiveAbTestGroups[abTest] = group;
    }

    public void RemoveMissingAbTestGroups(Predicate<string> isPresentPredicate)
    {
        var keysToRemove = new List<string>();

        foreach (var activeAbTest in _playerDo.ActiveAbTestGroups.Keys)
        {
            if (!isPresentPredicate(activeAbTest))
            {
                keysToRemove.Add(activeAbTest);
            }
        }

        foreach (var key in keysToRemove)
        {
            _playerDo.ActiveAbTestGroups.Remove(key);
            BDebug.Log($"Exiting ab test {key}");
        }
    }


    public void RemoveAbTestGroup(string abTest)
    {
        _playerDo.ActiveAbTestGroups.Remove(abTest);
    }

    public IEnumerable<(string, string)> GetActiveAbTestGroups()
    {
        foreach (var kvp in _playerDo.ActiveAbTestGroups)
        {
            yield return (kvp.Key, kvp.Value);
        }
    }

    public Dictionary<string, string> EndlessTreasureProgress => _playerDo.EndlessTreasureProgress;

    public PBDailyLoginData DailyLoginData => _playerDo.DailyLoginData;
    public Dictionary<string, BattlePassProgress> BattlePassProgress => _playerDo.BattlePassProgress;

    public List<string> AnsweredSurveys => _playerDo.AnsweredSurveys;
    public List<string> InteractedPromotions => _playerDo.PromotionData.InteractedPromotions;
    public Dictionary<string, int> AdminGifts
    {
        get => _playerDo.AdminGifts;
        set => _playerDo.AdminGifts = value;
    }

    public Dictionary<string, double> LastAppliedGiftEntity => _playerDo.LastAppliedGiftEntity;
    public long LastSeenMessageTimestamp
    {
        get => _playerDo.LastSeenMessageTimestamp;
        set => _playerDo.LastSeenMessageTimestamp = value;
    }

    public long LastSeenIceBreakerAnswersTimestamp
    {
        get => _playerDo.LastSeenIceBreakerAnswersTimestamp;
        set => _playerDo.LastSeenIceBreakerAnswersTimestamp = value;
    }

    public double GetLastInteractionTimestamp(string system)
    {
        if (_playerDo.LastInteractionTimestamps.TryGetValue(system, out var timestamp))
            return timestamp;
        return -1;
    }

    public void UpdateLastInteractionTimestamp(string system, double timestamp)
    {
        _playerDo.LastInteractionTimestamps[system] = timestamp;
    }

    public int WinRateAfterIapLevels => _playerDo.WinRateAfterIapLevels;
    public int WinAfterIapLevels => _playerDo.WinAfterIapLevels;
    public int AssistUidAfterIapLevels => _playerDo.AssistUidAfterIapLevels;

    public void SetAssistAfterIap(FBConfig.SystemConfig systemConfig)
    {
        _playerDo.WinRateAfterIapLevels = systemConfig.AssistWinRateAfterIap?.Levels ?? 0;
        _playerDo.WinAfterIapLevels = systemConfig.AssistWinAfterIap;
        _playerDo.AssistUidAfterIapLevels = systemConfig.AssistUidAfterIap?.Levels ?? 0;

        // Reset this variable so no level is discounted when the level ends
        AssistParams.UsingAssistAfterIap = false;
    }

    public void DecreaseAssistAfterIap()
    {
        _playerDo.WinRateAfterIapLevels = Math.Max(0, _playerDo.WinRateAfterIapLevels - 1);
        _playerDo.WinAfterIapLevels = Math.Max(0, _playerDo.WinAfterIapLevels - 1);
        _playerDo.AssistUidAfterIapLevels = Math.Max(0, _playerDo.AssistUidAfterIapLevels - 1);
    }

    public int CollectionCardsCount()
    {
        if (CollectionCards == null) return 0;
        var amount = 0;
        foreach (var (_, cards) in CollectionCards)
        {
            if (cards == null) continue;
            amount += cards.Count;
        }

        return amount;
    }

    public int WildCardTokensCount()
    {
        return WildCardTokenAmount % CollectionManager.TokensPerWildCard;
    }

    public int WildCardsCount()
    {
        return WildCardTokenAmount / CollectionManager.TokensPerWildCard;
    }

    public List<string> PendingToBeExecutedDeepLinks => _playerDo.PendingToBeExecutedDeepLinks;
    public List<string> PendingToBeConsumedDeepLinks => _playerDo.PendingToBeConsumedDeepLinks;
    public List<double> LastTeamVsTeamNudges => _playerDo.LastTeamVsTeamNudges;
    public DailyTaskState DailyTaskState => _playerDo.DailyTaskState;
    public Dictionary<string, int> ChallengesStartedByPlayerUid => _playerDo.ChallengesStartedByPlayerUid;
    public List<string> DiscardedChallengePlayerUids => _playerDo.DiscardedChallengePlayerUids;
    public Dictionary<string, int> ChallengeTriviaSeenTimes => _playerDo.ChallengeTriviaSeenTimes;
}