using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Modals;
using BBB.Navigation;
using BBB.UI.Core;
using Core.Configs;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Lua;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Promotions.Banners;
using GameAssets.Scripts.Promotions.Modal;
using PBConfig;
using PBGame;
using UnityEngine;
using UnityEngine.Profiling;

namespace GameAssets.Scripts.Promotions
{
    public partial class PromotionManager : IContextInitializable, IContextReleasable, IInterruptionTracker
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(FBConfig.UnifiedPromotionConfig),
            typeof(CountriesTiersConfig)
        };

        public event Action PromotionsInitialized;
        public event Action PromotionsRefreshed;

        private readonly Dictionary<string, List<Promotion>> _promotionsByCategories = new();
        private readonly Dictionary<string, Promotion> _promotionsByUid = new();
        private readonly Dictionary<string, Promotion> _activePromotions = new();
        private readonly List<Promotion> _cachedPromotionsList = new();
        private readonly List<Promotion> _cachedActivePromotionsWithIap = new();

        private readonly List<CountriesTiersConfig> _countriesTiersConfig = new();
        private readonly Dictionary<CountriesTiersConfig, string[]> _countriesTiersCodesCached = new();
        private readonly List<UniTask<GameObject>> _promotionsToPreloadTasks = new();

        private IConfig _config;
        private ILocationManager _locationManager;
        private IPlayerManager _playerManager;
        private LuaManager _luaManager;
        private TimeManager _timeManager;
        private IScreensManager _screensManager;
        private SimpleActionController _simpleActionController;

        private PBPromotionData _promotionData;
        private IAssetsManager _assetsManager;
        private IModalsBuilder _modalsBuilder;
        private IModalsManager _modalsManager;
        private ILevelsOrderingManager _levelsOrderingManager;
        private GameNotificationManager _notificationManager;

        private int _promotionsShowedTotalThisSession;
        private Dictionary<string, int> _promotionsShowedThisSession;

        private IPlayer Player => _playerManager.Player;
        private PBPromotionData PromotionData => Player.PlayerDO.PromotionData;
        public Dictionary<string, Promotion> ActivePromotions => _activePromotions;
        public Promotion CurrentlyShownPromotion { get; private set; }
        public HashSet<Promotion> LastShownPromotions { get; } = new();

        public void ResetTracking()
        {
            LastShownPromotions.Clear();
        }

        public void InitializeByContext(IContext context)
        {
            _config = context.Resolve<IConfig>();
            _playerManager = context.Resolve<IPlayerManager>();
            _luaManager = context.Resolve<LuaManager>();
            _timeManager = context.Resolve<TimeManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _simpleActionController = context.Resolve<SimpleActionController>();
            _locationManager = context.Resolve<ILocationManager>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _modalsManager = context.Resolve<IModalsManager>();
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _promotionData = Player.PromotionData;

            if (_promotionData == null)
                Debug.LogError("Promotion Data supposed to be initialized");
            SetupUnifiedPromotionConfig(_config);
            Config.OnConfigUpdated -= SetupUnifiedPromotionConfig;
            Config.OnConfigUpdated += SetupUnifiedPromotionConfig;
            _promotionsShowedTotalThisSession = 0;
            _promotionsShowedThisSession = new Dictionary<string, int>();
            
            _modalsManager.AddToInterruptionTracking(this);
        }

        public async UniTask ShowPromotionModal(string promotionUid,
            bool showInactive = false,
            Action onComplete = null)
        {
            if (!_activePromotions.TryGetValue(promotionUid, out var promotion) && !showInactive)
            {
                onComplete?.Invoke();
                return;
            }

            if (promotion == null && showInactive)
            {
                promotion = _promotionsByUid.GetSafe(promotionUid);
                if (promotion != null)
                {
                    var tasks = promotion.PreloadBundlesAsync();
                    await UniTask.WhenAll(tasks);
                }
            }

            if (promotion == null)
            {
                onComplete?.Invoke();
                return;
            }

            var genericPromoController = _modalsBuilder.CreateModalView<GenericPromoController>(ModalsType.GenericPromo);
            genericPromoController.Setup(promotion, onComplete);
            genericPromoController.ShowModal(promotion.GetShowMode());

            CurrentlyShownPromotion = promotion;
        }

        private void SetupUnifiedPromotionConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            
            InitCountries(config);
            var unifiedPromotionConfig = config.Get<FBConfig.UnifiedPromotionConfig>();
            if (unifiedPromotionConfig != null)
            {
                InitPromotions(unifiedPromotionConfig);
                _screensManager.OnScreenChanged -= ScreenChangedHandler;
                _screensManager.OnScreenChanged += ScreenChangedHandler;

                _modalsManager.ModalShownType -= ModalShownHandler;
                _modalsManager.ModalShownType += ModalShownHandler;
            }
            else
            {
                Debug.LogError("UnifiedPromotionConfig is missing");
            }
        }

        private void InitCountries(IConfig config)
        {
            _countriesTiersCodesCached.Clear();
            _countriesTiersConfig.Clear();
            var countriesTiersConfigsRaw = config.Get<CountriesTiersConfig>();

            if (countriesTiersConfigsRaw != null)
            {
                // Config without countries is considered default tier config.
                var isEmptyConfigFound = false;
                foreach (var item in countriesTiersConfigsRaw.Values)
                {
                    if (item?.Uid == null || !item.Countries.IsNullOrEmpty())
                        continue;

                    _countriesTiersConfig.Add(item);
                    isEmptyConfigFound = true;
                    break;
                }

                if (isEmptyConfigFound)
                {
                    foreach (var item in countriesTiersConfigsRaw.Values)
                    {
                        if (item?.Uid == null || _countriesTiersConfig.Contains(item))
                            continue;

                        _countriesTiersConfig.Add(item);
                    }
                }
                else
                {
                    // If no empty config found, then sort all remaining config by Uid in descending order,
                    // Config with highest uid (presumably with highest tier) will be selected as fallback config. -VK
                    foreach (var item in countriesTiersConfigsRaw.Values)
                    {
                        if (item?.Uid == null)
                            continue;

                        _countriesTiersConfig.Add(item);
                    }

                    int Comparison(CountriesTiersConfig a, CountriesTiersConfig b) => -string.Compare(a.Uid, b.Uid, StringComparison.Ordinal);
                    _countriesTiersConfig.Sort(Comparison);
                }

                foreach (var tierConfig in _countriesTiersConfig)
                {
                    _countriesTiersCodesCached[tierConfig] = tierConfig.Countries == null ? Array.Empty<string>() : tierConfig.Countries.Split(',', ';', '\n');
                }
            }
            else
            {
                BDebug.LogError(LogCat.Config, "Missing CountriesTiersConfig (not pushed to server?).");
            }
        }

        private void InitPromotions(IDictionary<string, FBConfig.UnifiedPromotionConfig> unifiedPromotionConfigs)
        {
            _activePromotions.Clear();
            _promotionsByCategories.Clear();
            _promotionsByUid.Clear();

            Profiler.BeginSample("-- InitializeAppContext UPM Config Process");

            foreach (var key in unifiedPromotionConfigs.Keys)
            {
                var unifiedPromotionConfig = unifiedPromotionConfigs[key];
                try
                {
                    var promotion = new Promotion(unifiedPromotionConfig);
                    promotion.Setup(_assetsManager, _levelsOrderingManager);

                    _promotionsByUid.Add(promotion.Uid, promotion);

                    if (!_promotionsByCategories.ContainsKey(promotion.Category))
                        _promotionsByCategories[promotion.Category] = new List<Promotion>();

                    _promotionsByCategories[promotion.Category].Add(promotion);
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.General, $"Exception while processing promotions key={key} count={unifiedPromotionConfigs.Keys.Count} message={e.Message}");
                }
            }

            // Sort promotions by category without LINQ
            foreach (var category in _promotionsByCategories.Keys)
            {
                var promotions = _promotionsByCategories[category];
                for (var i = 0; i < promotions.Count - 1; i++)
                {
                    for (var j = i + 1; j < promotions.Count; j++)
                    {
                        if ((promotions[i].AllowMultipleWithinCategory && !promotions[j].AllowMultipleWithinCategory) ||
                            (promotions[i].AllowMultipleWithinCategory == promotions[j].AllowMultipleWithinCategory &&
                             promotions[i].Priority > promotions[j].Priority))
                        {
                            (promotions[i], promotions[j]) = (promotions[j], promotions[i]);
                        }
                    }
                }
            }

            Profiler.EndSample();

            Refresh();
            PromotionsInitialized?.Invoke();
        }

        private void ScreenChangedHandler(ScreenType screenType, IScreensController arg2, IViewPresenter arg3)
        {
            if (screenType is ScreenType.EpisodeScreen)
            {
                Refresh();
            }
        }

        private void ModalShownHandler(ModalsType modalType)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
                return;

            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.ActiveModals.Contains(modalType) && promotion.CanAutoShow(_luaManager))
                {
                    InvokeAutoShowActions(promotion);
                    break;
                }
            }
        }

        public void Refresh()
        {
            EndActivePromotions();
            
            StartInactivePromotions();

            UpdateActivePromotionsNotifiers();

            PromotionsRefreshed?.Invoke();
        }

        private void UpdateActivePromotionsNotifiers()
        {
            if (!_notificationManager.IsInitialized)
                return;

            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.Notification.IsNullOrEmpty()) continue;

                var notifier = _notificationManager.GetNamedNotifier(promotion.Notification);
                if (promotion.CanAutoShow(_luaManager))
                {
                    notifier.SetNotifier(1);
                }
                else
                {
                    notifier.ResetNotifier();
                }
            }
        }

        private void EndActivePromotions()
        {
            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.ShouldBeDeactivated(_locationManager, _luaManager, _timeManager, this))
                {
                    _cachedPromotionsList.Add(promotion);
                }
            }

            foreach (var promotion in _cachedPromotionsList)
            {
                BDebug.Log(LogCat.Promotions, $"Deactivating promotion: {promotion.Uid}");
                _activePromotions.Remove(promotion.Uid);
                promotion.UnloadAssets();

                if (!_notificationManager.IsInitialized || promotion.Notification.IsNullOrEmpty()) continue;

                var notifier = _notificationManager.GetNamedNotifier(promotion.Notification);
                notifier.ResetNotifier();
            }

            _cachedPromotionsList.Clear();
        }

        private void StartInactivePromotions()
        {
            Profiler.BeginSample("StartInactivePromotions");
            var categoriesSnapshot = new Dictionary<string, List<Promotion>>();
            foreach (var kv in _promotionsByCategories)
            {
                categoriesSnapshot.Add(kv.Key, new List<Promotion>(kv.Value));
            }
        
            foreach (var kv in categoriesSnapshot)
            {
                var promotions = kv.Value;

                var forbiddenByActive = false;
                foreach (var promotion in promotions)
                {
                    if (promotion.AllowMultipleWithinCategory || !_activePromotions.ContainsKey(promotion.Uid))
                        continue;

                    forbiddenByActive = true;
                    break;
                }

                if (forbiddenByActive)
                {
                    continue;
                }

                foreach (var promotion in promotions)
                {
                    if (_activePromotions.ContainsKey(promotion.Uid) || !promotion.CanBeActivated(_locationManager, _timeManager, this))
                        continue;
                
                    var shouldReset = promotion.IsCycled ? !WasActiveInCurrentCycle(promotion.Uid) : !WasActive(promotion.Uid);
                    if (shouldReset)
                    {
                        promotion.ExecuteResetCall(_luaManager);
                    }

                    // splitting activation check as predicates is the most performance costly and we need to call reset only just before we checking the predicates
                    // but we should execute activation call only once per activation/cycle
                    if (!promotion.CanBeActivatedByPredicates(_luaManager))
                        continue;

                    BDebug.Log(LogCat.Promotions, $"Activating promotion: {promotion.Uid}");
                    _activePromotions.Add(promotion.Uid, promotion);

                    if (shouldReset)
                    {
                        SetPromotionActivationTime(promotion.Uid, _timeManager.CurrentTimeStamp());
                    }

                    if (!promotion.AllowMultipleWithinCategory)
                        break;
                }
            }
            Profiler.EndSample();
        }

        public bool ShouldAutoShow(ScreenType screenType, PromotionAutoShowPriority autoShowPriority)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
                return false;

            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.AutoShowPriority == autoShowPriority && promotion.ActiveScreens.Contains(screenType) && promotion.CanAutoShow(_luaManager))
                {
                    return true;
                }
            }

            return false;
        }

        public void AutoShow(ScreenType screenType, PromotionAutoShowPriority autoShowPriority)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
                return;

            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.AutoShowPriority != autoShowPriority ||
                    !promotion.ActiveScreens.Contains(screenType) ||
                    !promotion.CanAutoShow(_luaManager)) continue;

                InvokeAutoShowActions(promotion);
                break;
            }
        }

        public IReadOnlyList<Promotion> GetActivePromotionsWithIap()
        {
            _cachedActivePromotionsWithIap.Clear();

            foreach (var promotion in _activePromotions.Values)
            {
                if (promotion.ShouldShowIap)
                {
                    _cachedActivePromotionsWithIap.Add(promotion);
                }
            }

            return _cachedActivePromotionsWithIap;
        }

        private void InvokeAutoShowActions(Promotion promotion)
        {
            BDebug.Log(LogCat.Promotions, $"InvokeAutoShowActions: {promotion.Uid}");

            PurchasePath.Start(PurchaseStep.AutoPopup);
            PurchasePath.Append(promotion.AnalyticsItem);

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, promotion.AnalyticsFamily, promotion.AnalyticsItem));
            DauInteractions.TapOnAutoPopups.AwaitLogs(promotion.AnalyticsFamilyClicks, promotion.AnalyticsFamilyClose);
            ExecuteActions(promotion, promotion.AutoShowActions).Forget();
        }

        public void InvokeHudActions(Promotion promotion)
        {
            BDebug.Log(LogCat.Promotions, $"InvokeHudActions: {promotion.Uid}");

            PurchasePath.Start(PurchaseStep.Hud);
            PurchasePath.Append(promotion.AnalyticsItem);

            var notifierStatus = IsPromotionNotShownThisSession(promotion.Uid) ? 1 : 0;
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, promotion.AnalyticsFamily, promotion.AnalyticsItem, notifierStatus));
            ExecuteActions(promotion, promotion.HudActions).Forget();
        }

        public async UniTask InvokeBannerActions(Promotion promotion, BannerPlacement bannerPlacement)
        {
            BDebug.Log(LogCat.Promotions, $"InvokeBannerActions: {promotion.Uid}");

            PurchasePath.Start(PurchaseStep.Banner);
            PurchasePath.Append(DauInteractions.TapOnBanner.GetAnalyticsName(bannerPlacement));
            PurchasePath.Append(promotion.AnalyticsItem);

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnBanner.Name, promotion.AnalyticsFamily, promotion.AnalyticsItem));
            await ExecuteActions(promotion, promotion.BannerActions);
        }

        private async UniTask ExecuteActions(Promotion promotion, List<PromotionAction> actions)
        {
            LastShownPromotions.Add(promotion);
            MarkPromotionAsShown(promotion.Uid);
            Player.UpdateLastInteractionTimestamp(promotion.Uid, _timeManager.CurrentTimeStamp());
            _playerManager.MarkDirty();

            if (actions.IsNullOrEmpty())
            {
                DoneHandler();
                return;
            }

            foreach (var promotionAction in actions)
            {
                await _simpleActionController.ExecuteActionAsync(promotionAction.ActionUid, promotionAction.ActionParams);
            }

            DoneHandler();
            return;

            void DoneHandler()
            {
                Refresh();
            }
        }

        public void ReleaseByContext(IContext context)
        {
            if (_screensManager != null)
            {
                _screensManager.OnScreenChanged -= ScreenChangedHandler;
            }

            if (_modalsManager != null)
            {
                _modalsManager.ModalShownType -= ModalShownHandler;
            }

            Config.OnConfigUpdated -= SetupUnifiedPromotionConfig;

            foreach (var promotion in _activePromotions.Values)
            {
                promotion.UnloadAssets();
            }

            _activePromotions.Clear();
            _promotionsByCategories.Clear();
        }

        private void SetPromotionActivationTime(string promotionUid, double timestamp)
        {
            _promotionData.PromotionsActivationTime[promotionUid] = timestamp;
        }

        public double GetPromotionActivationTime(string promotionUid)
        {
            return _promotionData.PromotionsActivationTime.GetValueOrDefault(promotionUid, double.MaxValue);
        }

        private void MarkPromotionAsShown(string promotionUid)
        {
            if (promotionUid.IsNullOrEmpty())
                return;

            _promotionsShowedTotalThisSession++;

            _promotionsShowedThisSession.TryAdd(promotionUid, 0);
            _promotionsShowedThisSession[promotionUid] += 1;

            PromotionData.PromotionsDisplayCount.TryAdd(promotionUid, 0);
            PromotionData.PromotionsDisplayCount[promotionUid] += 1;

            PromotionData.PromotionsLastShownSessionNumber[promotionUid] = Player.GetNumberOfSessions();
        }

        internal List<UniTask<GameObject>> GetPromotionsToPreloadLoad(string screenBeingLoaded, ScreenType screenType)
        {
            _promotionsToPreloadTasks.Clear();
            foreach (var promotion in ActivePromotions.Values)
            {
                var tasks = promotion.PreloadBundlesAsync();
                if (tasks.Count == 0)
                {
                    continue;
                }

                _promotionsToPreloadTasks.AddRange(tasks);
            }

            return _promotionsToPreloadTasks;
        }
    }
}