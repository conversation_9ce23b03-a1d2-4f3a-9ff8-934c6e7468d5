using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.ResourcesManager;
using BBB.Core.ResourcesManager.Asset;
using BBB.Navigation;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Lua;
using GameAssets.Scripts.Promotions.Banners;
using GameAssets.Scripts.Promotions.Modal;
using UnityEngine;

namespace GameAssets.Scripts.Promotions
{
    public class Promotion
    {
        private const int NumberOfLevelsToLoadBundles = 1;

        private readonly UnifiedPromotionConfig _promotionConfig;
        private readonly List<DayOfWeek> _activeWeekDays;

        private readonly DateTime _startDateTime;
        private readonly DateTime _endDateTime;

        private readonly List<UniTask<GameObject>> _preloadTasks = new();

        private IAssetsManager _assetsManager;

        private GameObject _hudPrefab;

        private GameObject _modalPrefab;

        private GameObject _bannerPrefab;

        private GameObject _iapPrefab;

        private string _minLevelToLoadBundles;
        private string _maxLevelToLoadBundles;

        private string _startLevelUid;
        private string _endLevelUid;

        public string Uid => _promotionConfig.Uid;
        public string Category => _promotionConfig.CategoryParams?.Category;
        public int Priority => _promotionConfig.CategoryParams?.Priority ?? int.MaxValue;
        public bool AllowMultipleWithinCategory => _promotionConfig.CategoryParams?.AllowMultiple ?? false;
        public string Notification => _promotionConfig.Notification;
        public bool IsCycled { get; }
        public bool IsExpirable { get; }
        public PromotionAutoShowPriority AutoShowPriority { get; } = PromotionAutoShowPriority.Low;
        public string AnalyticsFamily { get; }
        public string AnalyticsItem { get; }

        public string AnalyticsFamilyClose { get; }
        public string AnalyticsFamilyClicks { get; }

        public List<PromotionAction> AutoShowActions { get; }
        public List<PromotionAction> HudActions { get; }
        public List<PromotionAction> BannerActions { get; }


        public HashSet<ScreenType> ActiveScreens { get; } = new();
        public HashSet<ModalsType> ActiveModals { get; } = new();

        public bool ShouldShowHudIcon => _hudPrefab != null && IsModalNullOrReady;
        public bool ShouldShowBanner => _bannerPrefab != null;
        public bool ShouldShowIap => _iapPrefab != null;
        private bool IsModalNullOrReady => _promotionConfig.ModalPrefab.IsNullOrEmpty() || _modalPrefab != null;

        private bool ShouldPreloadHudBundle => !_promotionConfig.HudPrefab.IsNullOrEmpty() && _hudPrefab == null;
        private bool ShouldPreloadModalBundle => !_promotionConfig.ModalPrefab.IsNullOrEmpty() && _modalPrefab == null;
        private bool ShouldPreloadBannerBundle => !_promotionConfig.BannerPrefab.IsNullOrEmpty() && _bannerPrefab == null;
        private bool ShouldPreloadIAPBundle => !_promotionConfig.IapPrefab.IsNullOrEmpty() && _iapPrefab == null;

        public Banner Banner { get; set; }

        public Promotion(UnifiedPromotionConfig promotionConfig, int overrideEndDate = -1)
        {
            _promotionConfig = promotionConfig;

            _activeWeekDays = GetActiveWeekdays();

            IsCycled = !_activeWeekDays.IsNullOrEmpty();
            IsExpirable = _promotionConfig.EndTime.HasValue || promotionConfig.Duration > float.Epsilon || IsCycled;

            _startDateTime = ToDateTime(_promotionConfig.StartTime, DateTime.SpecifyKind(DateTime.MinValue, DateTimeKind.Local));
            _endDateTime = overrideEndDate > 0
                ? Util.UnixTimeStampToDateTime(overrideEndDate)
                : ToDateTime(_promotionConfig.EndTime, DateTime.SpecifyKind(DateTime.MaxValue, DateTimeKind.Local));

            if (_promotionConfig.ActiveScreensLength > 0)
            {
                for (var i = 0; i < _promotionConfig.ActiveScreensLength; i++)
                {
                    var activeScreenName = _promotionConfig.ActiveScreens(i);
                    if (Enum.TryParse(activeScreenName, true, out ScreenType activeScreen))
                    {
                        ActiveScreens.Add(activeScreen);
                    }
                    else if (Enum.TryParse(activeScreenName, true, out ModalsType modalsType))
                    {
                        ActiveModals.Add(modalsType);
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Promotions, $"Couldn't parse: {activeScreenName}");
                    }
                }
            }
            else
            {
                ActiveScreens.Add(ScreenType.EpisodeScreen);
            }

            if (!_promotionConfig.AutoshowPriority.IsNullOrEmpty())
            {
                if (Enum.TryParse(_promotionConfig.AutoshowPriority, true, out PromotionAutoShowPriority autoShowPriority))
                {
                    AutoShowPriority = autoShowPriority;
                }
                else
                {
                    BDebug.LogError(LogCat.Promotions, $"Couldn't parse: {_promotionConfig.AutoshowPriority}");
                }
            }

            AutoShowActions = new List<PromotionAction>();
            HudActions = AutoShowActions;
            BannerActions = AutoShowActions;

            if (_promotionConfig.AutoshowActionsLength > 0)
            {
                for (var i = 0; i < _promotionConfig.AutoshowActionsLength; i++)
                {
                    var action = _promotionConfig.AutoshowActions(i);
                    if (action != null)
                        AutoShowActions.Add(new PromotionAction(action.Value));
                }
            }
            else
            {
                if (!_promotionConfig.ModalPrefab.IsNullOrEmpty())
                {
                    AutoShowActions.Add(new PromotionAction("show_promotion", new Dictionary<string, string>()
                    {
                        { "promotion_uid", _promotionConfig.Uid }
                    }));
                }
            }

            if (_promotionConfig.HudActionsLength > 0)
            {
                HudActions = new List<PromotionAction>();

                for (var i = 0; i < _promotionConfig.HudActionsLength; i++)
                {
                    var action = _promotionConfig.HudActions(i);
                    if (action != null)
                        HudActions.Add(new PromotionAction(action.Value));
                }
            }

            if (_promotionConfig.BannerActionsLength > 0)
            {
                BannerActions = new List<PromotionAction>();

                for (var i = 0; i < _promotionConfig.BannerActionsLength; i++)
                {
                    var action = _promotionConfig.BannerActions(i);
                    if (action != null)
                        BannerActions.Add(new PromotionAction(action.Value));
                }
            }

            AnalyticsFamily = DauInteractions.AutoPopups.Promotions;
            AnalyticsItem = _promotionConfig.Uid;
            if (!_promotionConfig.Analytics.IsNullOrEmpty())
            {
                var strings = _promotionConfig.Analytics.Split(',');
                if (strings.Length >= 1)
                {
                    AnalyticsFamily = strings[0].Trim();
                }

                if (strings.Length >= 2)
                {
                    AnalyticsItem = strings[1].Trim();
                }
            }

            AnalyticsFamilyClose = $"{AnalyticsFamily}_close";
            AnalyticsFamilyClicks = $"{AnalyticsFamily}_clicks";
        }

        public void Setup(IAssetsManager assetsManager, ILevelsOrderingManager levelsOrderingManager)
        {
            _assetsManager = assetsManager;

            // StartLevelConditionLength is an array with up to 2 elements
            // First element is the min level required for this promo to be shown -- inclusive
            // Second element is the max level required for this promo to be shown -- exclusive
            if (_promotionConfig.StartLevelConditionLength > 0)
            {
                _startLevelUid = _promotionConfig.StartLevelCondition(0);
                _minLevelToLoadBundles = _startLevelUid;

                for (var i = 0; i < NumberOfLevelsToLoadBundles; i++)
                {
                    var previousLevelUid = levelsOrderingManager.GetPreviousLevelUid(_minLevelToLoadBundles);
                    if (previousLevelUid.IsNullOrEmpty())
                        break;

                    _minLevelToLoadBundles = previousLevelUid;
                }

                if (_promotionConfig.StartLevelConditionLength >= 2)
                {
                    _endLevelUid = _promotionConfig.StartLevelCondition(1);
                    _maxLevelToLoadBundles = _endLevelUid;
                }
            }
        }

        public GameObject GetHudPrefab()
        {
            if (_hudPrefab != null)
                return _hudPrefab;

            BDebug.LogError(LogCat.Promotions, $"Hud is still loading: {Uid}");
            return null;
        }

        public ShowMode GetShowMode()
        {
            return _modalPrefab != null ? _modalPrefab.GetComponent<PromotionView>().ShowMode : ShowMode.Delayed;
        }

        public GameObject GetModalPrefab()
        {
            if (_modalPrefab != null)
                return _modalPrefab;

            BDebug.LogError(LogCat.Promotions, $"Modal is still loading: {Uid}");
            return null;
        }

        public GameObject GetBannerPrefab()
        {
            if (_bannerPrefab != null)
                return _bannerPrefab;

            BDebug.LogError(LogCat.Promotions, $"Banner is still loading: {Uid}");
            return null;
        }

        public GameObject GetIapPrefab()
        {
            if (_iapPrefab != null)
                return _iapPrefab;

            BDebug.LogError(LogCat.Promotions, $"Iap is still loading: {Uid}");
            return null;
        }

        public List<UniTask<GameObject>> PreloadBundlesAsync()
        {
            _preloadTasks.Clear();

            if (ShouldPreloadModalBundle)
            {
                _preloadTasks.Add(PreloadPrefabAsync(_promotionConfig.ModalPrefab).ContinueWith(assetLoaded => _modalPrefab = assetLoaded?.Get()));
            }

            if (ShouldPreloadHudBundle)
            {
                _preloadTasks.Add(PreloadPrefabAsync(_promotionConfig.HudPrefab).ContinueWith(assetLoaded => _hudPrefab = assetLoaded?.Get()));
            }

            if (ShouldPreloadBannerBundle)
            {
                _preloadTasks.Add(PreloadPrefabAsync(_promotionConfig.BannerPrefab).ContinueWith(assetLoaded => _bannerPrefab = assetLoaded?.Get()));
            }

            if (ShouldPreloadIAPBundle)
            {
                _preloadTasks.Add(PreloadPrefabAsync(_promotionConfig.IapPrefab).ContinueWith(assetLoaded => _iapPrefab = assetLoaded?.Get()));
            }

            return _preloadTasks;
        }

        private async UniTask<IAssetLoaded<GameObject>> PreloadPrefabAsync(string prefabName)
        {
            prefabName = prefabName?.Trim().ToLowerInvariant();
            if (prefabName.IsNullOrEmpty())
                return null;
            
            IAssetLoaded<GameObject> assetLoaded = null;
            BDebug.Log(LogCat.Promotions, $"Preloading bundles {Uid} prefab={prefabName}");
            try
            {
                assetLoaded = await _assetsManager.LoadAsync<GameObject>(prefabName);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.Promotions, $"Failed to preload bundle for promotion {Uid} prefab={prefabName}: {e.Message}");
            }

            return assetLoaded;
        }

        public bool IsTimestampInCurrentCycle(double timeStamp, TimeManager timeManager)
        {
            if (!IsCycled)
                return false;

            GetCurrentCycleDateTime(timeManager, out var cycleStartDateTime, out var cycleEndDateTime);
            return timeStamp >= cycleStartDateTime.ToUnixTimeSeconds() && timeStamp <= cycleEndDateTime.ToUnixTimeSeconds();
        }

        public bool CanBeActivated(ILocationManager locationManager, TimeManager timeManager, PromotionManager promotionManager)
        {
            return CanStartByLevel(locationManager) &&
                   CanStartByDate(timeManager) &&
                   CanStartByCycle(timeManager) &&
                   !IsExpired(timeManager, promotionManager);
        }

        public bool CanBeActivatedByPredicates(LuaManager luaManager)
        {
            return CanStartByPredicate(luaManager) &&
                   !ShouldEndByPredicate(luaManager);
        }

        public bool ShouldBeDeactivated(ILocationManager locationManager, LuaManager luaManager, TimeManager timeManager, PromotionManager promotionManager)
        {
            return IsExpired(timeManager, promotionManager)
                   || ShouldEndByCycle(timeManager, promotionManager)
                   || ShouldEndByLevel(locationManager)
                   || ShouldEndByPredicate(luaManager);
        }

        public bool CanAutoShow(LuaManager luaManager)
        {
            if (_promotionConfig.AutoshowCondition.IsNullOrEmpty())
                return false;

            if (!IsModalNullOrReady)
                return false;

            UnityEngine.Profiling.Profiler.BeginSample($"UnifiedPromotion CanAutoShow [{_promotionConfig.Uid}]");
            var ret = luaManager.EvaluateExpression(_promotionConfig.AutoshowCondition);
            UnityEngine.Profiling.Profiler.EndSample();
            return ret;
        }

        public double GetExpirationTime(TimeManager timeManager, PromotionManager promotionManager)
        {
            if (!IsExpirable)
                return double.MaxValue;

            var endDateTimestamp = _endDateTime.ToUnixTimeSeconds();
            var durationEndTime = double.MaxValue;

            if (_promotionConfig.Duration > float.Epsilon && promotionManager.WasActive(Uid))
                durationEndTime = promotionManager.GetPromotionActivationTime(Uid) + _promotionConfig.Duration;

            GetCurrentCycleDateTime(timeManager, out var cycleStartDateTime, out var cycleEndDateTime);

            return Math.Min(cycleEndDateTime.ToUnixTimeSeconds(), Math.Min(durationEndTime, endDateTimestamp));
        }

        public void ExecuteResetCall(LuaManager luaManager)
        {
            if (_promotionConfig.ActivationCall.IsNullOrEmpty())
                return;

            var activationCalls = _promotionConfig.ActivationCall.Split(';');
            foreach (var activationSubCall in activationCalls)
            {
                var trimmedCall = activationSubCall.Trim();
                luaManager.Evaluate(trimmedCall);
            }
        }

        private bool CanStartByLevel(ILocationManager locationManager)
        {
            return (_startLevelUid.IsNullOrEmpty() || locationManager.GetLevelStage(_startLevelUid) > 0)
                   && (_endLevelUid.IsNullOrEmpty() || locationManager.GetLevelStage(_endLevelUid) <= 0);
        }

        private bool CanStartByDate(TimeManager timeManager)
        {
            return timeManager.GetCurrentDateTime() >= _startDateTime.ToUniversalTime();
        }

        private bool CanStartByCycle(TimeManager timeManager)
        {
            if (!IsCycled)
                return true;

            GetCurrentCycleDateTime(timeManager, out var cycleStartDateTime, out var cycleEndDateTime);
            return timeManager.GetCurrentDateTime() >= cycleStartDateTime.ToUniversalTime();
        }

        private bool CanStartByPredicate(LuaManager luaManager)
        {
            if (_promotionConfig.StartCondition.IsNullOrEmpty())
                return true;

            UnityEngine.Profiling.Profiler.BeginSample($"UnifiedPromotion CanStartByPredicate [{_promotionConfig.Uid}]");
            var ret = luaManager.EvaluateExpression(_promotionConfig.StartCondition);
            UnityEngine.Profiling.Profiler.EndSample();

            return ret;
        }

        private bool IsExpired(TimeManager timeManager, PromotionManager promotionManager)
        {
            return timeManager.CurrentTimeStamp() > GetExpirationTime(timeManager, promotionManager);
        }

        private bool ShouldEndByLevel(ILocationManager locationManager)
        {
            return !_endLevelUid.IsNullOrEmpty() && locationManager.GetLevelStage(_endLevelUid) > 0;
        }

        private bool ShouldEndByPredicate(LuaManager luaManager)
        {
            if (_promotionConfig.EndCondition.IsNullOrEmpty())
                return false;

            UnityEngine.Profiling.Profiler.BeginSample($"UnifiedPromotion ShouldEndByPredicate [{_promotionConfig.Uid}]");
            var ret = luaManager.EvaluateExpression(_promotionConfig.EndCondition);
            UnityEngine.Profiling.Profiler.EndSample();
            return ret;
        }

        private bool ShouldEndByCycle(TimeManager timeManager, PromotionManager promotionManager)
        {
            if (!IsCycled)
                return false;

            if (!promotionManager.WasActive(Uid))
                return false;

            GetCurrentCycleDateTime(timeManager, out var cycleStartDateTime, out var cycleEndDateTime);
            return promotionManager.GetPromotionActivationTime(Uid) < cycleStartDateTime.ToUnixTimeSeconds();
        }

        private List<DayOfWeek> GetActiveWeekdays()
        {
            if (_promotionConfig.WeeklySchedule.IsNullOrEmpty())
                return null;

            var activeWeekDays = new List<DayOfWeek>();
            var activeWeekDaysStrings = _promotionConfig.WeeklySchedule.Split(',');

            foreach (var activeWeekDaysString in activeWeekDaysStrings)
            {
                var trimmed = activeWeekDaysString.Trim();
                var weekDayEnum = trimmed.TryParseToEnum<DayOfWeek>(true);
                activeWeekDays.Add(weekDayEnum);
            }

            return activeWeekDays;
        }

        // can't be cached as exact cycle depends on current time so should be recalculated on each request
        private void GetCurrentCycleDateTime(TimeManager timeManager, out DateTime cycleStartDateTime, out DateTime cycleEndDateTime)
        {
            cycleStartDateTime = _startDateTime;
            cycleEndDateTime = _endDateTime;

            if (_activeWeekDays.IsNullOrEmpty())
                return;

            // taking this as as the anchor of some nearly real time only as we do not know yet have we scheduled things locally or not
            // we will update the cycle to precise dates later below
            var currentDateTime = timeManager.GetCurrentDateTime();

            var firstWeekDayOfCycle = (int)_activeWeekDays[0];
            var lastWeekDayOfCycle = (int)_activeWeekDays[^1];

            if (lastWeekDayOfCycle <= firstWeekDayOfCycle)
                lastWeekDayOfCycle += 7;

            // merging Date with HS and time kind startDateTime params
            var cycleStartDateTimeAnchor = new DateTime(currentDateTime.Year, currentDateTime.Month, currentDateTime.Day, _startDateTime.Hour, _startDateTime.Minute, 0, _startDateTime.Kind);
            // and making sure it is exact day
            cycleStartDateTime = cycleStartDateTimeAnchor.AddDays(firstWeekDayOfCycle - (int)cycleStartDateTimeAnchor.DayOfWeek);

            // same for end time
            var cycleEndDateTimeAnchor = new DateTime(currentDateTime.Year, currentDateTime.Month, currentDateTime.Day, _endDateTime.Hour, _endDateTime.Minute, 0, _endDateTime.Kind);
            cycleEndDateTime = cycleEndDateTimeAnchor.AddDays(lastWeekDayOfCycle - (int)cycleEndDateTimeAnchor.DayOfWeek);

            // now we need to make sure that the cycle is up to current time
            // definition of current cycle is end time cycle is closest of the bigger's than current time

            cycleStartDateTime = cycleStartDateTime.AddDays(-14);
            cycleEndDateTime = cycleEndDateTime.AddDays(-14);

            while (cycleEndDateTime.ToUniversalTime() <= currentDateTime)
            {
                cycleStartDateTime = cycleStartDateTime.AddDays(7);
                cycleEndDateTime = cycleEndDateTime.AddDays(7);
            }
        }

        private static DateTime ToDateTime(DayMonthYear? dayMonthYear, DateTime fallbackValue)
        {
            if (!dayMonthYear.HasValue)
                return fallbackValue;

            var dayMonthYearValue = dayMonthYear.Value;
            return new DateTime(dayMonthYearValue.Year, dayMonthYearValue.Month, dayMonthYearValue.Day, dayMonthYearValue.Hour, dayMonthYearValue.Minute, 0,
                dayMonthYearValue.UseLocalTime ? DateTimeKind.Local : DateTimeKind.Utc);
        }

        internal void UnloadAssets()
        {
            UnloadPrefab(_promotionConfig.HudPrefab);
            UnloadPrefab(_promotionConfig.ModalPrefab);
            UnloadPrefab(_promotionConfig.BannerPrefab);
            UnloadPrefab(_promotionConfig.IapPrefab);
        }

        private void UnloadPrefab(string prefabName)
        {
            prefabName = prefabName?.Trim().ToLowerInvariant();
            if (prefabName.IsNullOrEmpty())
                return;
            _assetsManager.UnloadAsset(prefabName);
        }
    }
}