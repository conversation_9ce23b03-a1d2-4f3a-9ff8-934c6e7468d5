using System.Collections.Generic;
using BBB;
using BBB.DI;
using BebopBee.Core;
using DG.Tweening;
using GameAssets.Scripts.Core.TimeManager;
using UnityEngine;

namespace GameAssets.Scripts.Promotions.Hud
{
    public class PromotionHudManager : CustomHudManager
    {
        [SerializeField] private Transform _root;
        [SerializeField] private GameObject _promotionHudViewHandler;

        private readonly Dictionary<Promotion, PromotionHudViewHandler> _promotionHudViewHandlers = new();
        private readonly List<Promotion> _promotionsToRemove = new();
        private IContext _context;

        private GenericHudManager _genericHudManager;
        private PromotionManager _promotionManager;
        private TimeManager _timeManager;

        private Tweener _autoRefreshTweener;

        public override void Init(IContext context)
        {
            _genericHudManager = context.Resolve<GenericHudManager>();
            _promotionManager = context.Resolve<PromotionManager>();
            _timeManager = context.Resolve<TimeManager>();

            _context = context;

            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _promotionManager.PromotionsInitialized += PromotionsInitializedHandler;
            _promotionManager.PromotionsRefreshed += RefreshInternal;
        }

        private void Unsubscribe()
        {
            if (_promotionManager != null)
            {
                _promotionManager.PromotionsInitialized -= PromotionsInitializedHandler;
                _promotionManager.PromotionsRefreshed -= RefreshInternal;
            }
        }

        private void PromotionsInitializedHandler()
        {
            foreach (var hudView in _promotionHudViewHandlers.Values)
            {
                Destroy(hudView.gameObject);
            }

            _promotionHudViewHandlers.Clear();
            _autoRefreshTweener?.Kill();
            _autoRefreshTweener = null;

            if (_genericHudManager.IsHudVisible)
            {
                Refresh();
            }
        }

        public override void Refresh()
        {
            if (_promotionManager == null)
            {
                return;
            }

            _promotionManager.Refresh();
            // here, when we call for refresh, we will receive event PromotionsRefreshed which will call RefreshInternal
        }

        private void RefreshInternal()
        {
            var activePromotions = _promotionManager.ActivePromotions;

            _promotionsToRemove.Clear();
            foreach (var promotion in _promotionHudViewHandlers.Keys)
            {
                if (!activePromotions.ContainsKey(promotion.Uid))
                {
                    _promotionsToRemove.Add(promotion);
                }
            }

            foreach (var promotion in _promotionsToRemove)
            {
                Destroy(_promotionHudViewHandlers[promotion].gameObject);
                _promotionHudViewHandlers.Remove(promotion);
            }

            var minimumExpirationTime = double.MaxValue;
            _autoRefreshTweener?.Kill();

            foreach (var activePromotion in activePromotions.Values)
            {
                if (!activePromotion.ShouldShowHudIcon) continue;

                if (activePromotion.IsExpirable)
                {
                    var expirationTime = activePromotion.GetExpirationTime(_timeManager, _promotionManager);
                    if (expirationTime < minimumExpirationTime)
                    {
                        minimumExpirationTime = expirationTime;
                    }
                }

                if (_promotionHudViewHandlers.ContainsKey(activePromotion))
                    continue;

                UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_promotionHudViewHandler.name}]");
                var go = Instantiate(_promotionHudViewHandler, _root);
                UnityEngine.Profiling.Profiler.EndSample();
                go.name = $"Promotion: {activePromotion.Uid}";
                go.transform.SetAsLastSibling();

                var promotionHudViewHandler = go.GetComponent<PromotionHudViewHandler>();
                promotionHudViewHandler.Init(_context);
                promotionHudViewHandler.Setup(activePromotion);
                promotionHudViewHandler.SetupPrefab(activePromotion.GetHudPrefab());

                _promotionHudViewHandlers[activePromotion] = promotionHudViewHandler;
            }

            if (minimumExpirationTime != double.MaxValue && minimumExpirationTime > _timeManager.CurrentTimeStamp())
            {
                _autoRefreshTweener = Rx.Invoke((float)(minimumExpirationTime - _timeManager.CurrentTimeStamp()) + 1, _ => { Refresh(); });
            }

            foreach (var promotionHudViewHandler in _promotionHudViewHandlers.Values)
            {
                promotionHudViewHandler.Refresh();
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            foreach (var hudView in _promotionHudViewHandlers.Values)
            {
                Destroy(hudView.gameObject);
            }

            _promotionHudViewHandlers.Clear();
            _autoRefreshTweener?.Kill();
            _autoRefreshTweener = null;

            _context = null;

            Unsubscribe();
        }
    }
}