using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Promotions;
using UnityEngine;

namespace BBB
{
    public sealed class PromotionsResourceManager : IContextInitializable, ISpecializedResourceManager
    {
        private PromotionManager _promotionManager;

        public bool IsRequired => true;
        private int _tasksLeftToLoad;

        public void Reset() {}

        public void InitializeByContext(IContext context)
        {
            _promotionManager = context.Resolve<PromotionManager>();
        }

        public async UniTask TryReloadAsync(string screenBeingLoaded, ScreenType screenType)
        {
            var promotions = _promotionManager.GetPromotionsToPreloadLoad(screenBeingLoaded, screenType);

            _tasksLeftToLoad = promotions.Count;

            await UniTask.WhenAll(promotions);
            _tasksLeftToLoad = 0;
        }

        public bool IsLoading(string screenName, string prevScreenName) => _tasksLeftToLoad > 0;

        public bool HasFailed() => false;

        public void ResetFailure() {}

        public float Progress()
        {
            return _tasksLeftToLoad == 0 ? 1f : 0f;
        }

        public void DisposeForScreen(ScreenType screenType, ScreenType currentScreenType)
        {
            // Assets will be disposed by PromotionManager
        }
    }
}