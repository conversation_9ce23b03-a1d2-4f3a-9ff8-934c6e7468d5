using System;
using System.Collections;
using BBB;
using BBB.Core.Analytics;
using BBB.UI;
using BBB.UI.LoopScrollRect;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Generic.Views;
using JetBrains.Annotations;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Collection.UI
{
    public class CollectionSetItem : LoopScrollListItem, IScrollable, INotifierStatus
    {
        public const string CollectionLockedSetLevel = "COLLECTION_LOCKED_SET_LEVEL";
        private const string ComingSoonButtonTitle = "COMING_SOON_BUTTON_TITLE";
        
        [SerializeField] private LocalizedTextPro _nameText;
        [SerializeField] private Image _icon;
        [SerializeField] private Button _setButton;
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField] private GenericRewardComponent _reward;
        [SerializeField] private MaterialSwapper _grayscaleSwapper;
        [SerializeField] private NotifierWidget _notifierWidget;
        [SerializeField] private Button _rewardButton;
        [SerializeField] private LocalizedTextPro _lockedText;
        [SerializeField] private Button _openButton;
        [SerializeField] private Animator _animator;
        [SerializeField] private Button _lockedButton;
        [SerializeField] private Transform _centerPoint;
        [SerializeField] private CollectionSetItemTouchHandler[] _touchHandlers;
        [SerializeField] private Transform _speechBubbleTransform;

        private CollectionSetViewModel _model;

        public Transform CenterPoint => _centerPoint;
        public event Action<string> CardsModalButtonPressed;
        public event Action<string> OpenButtonPressed;
        public event Action<string, Transform> LockButtonPressed;
        public event Action<string> StateSeen;
        public event Action StatusUpdated;
        public RectTransform MaskRect;
        private Coroutine _transitionCoroutine;

        private static readonly int LockedHash = Animator.StringToHash("Locked");
        private static readonly int ClickLockedHash = Animator.StringToHash("ClickLocked");
        private static readonly int UnlockedHash = Animator.StringToHash("Unlocked");
        private static readonly int UnlockHash = Animator.StringToHash("Unlock");
        private static readonly int CompleteHash = Animator.StringToHash("Complete");
        private static readonly int CompletedHash = Animator.StringToHash("Completed");
        private static readonly int ClaimedHash = Animator.StringToHash("Claimed");
        private static readonly int ClaimHash = Animator.StringToHash("Claim");
        
        private void Awake()
        {
            _setButton.ReplaceOnClick(OpenSet);
            _openButton.ReplaceOnClick(OpenButtonHandler);
            _rewardButton.ReplaceOnClick(RewardButtonHandler);
            _lockedButton.ReplaceOnClick(LockedButtonHandler);
            _notifierWidget.Init(this);
        }

        private void LockedButtonHandler()
        {
            _animator.ResetAllParameters();
            _animator.SetTrigger(ClickLockedHash);
            LockButtonPressed.SafeInvoke(_model.UnlockLevelName, _centerPoint);
        }

        private void OpenButtonHandler()
        {
            if (_openButton.interactable)
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.Collection, DauInteractions.Collection.Claim));
                OpenButtonPressed.SafeInvoke(_model.Uid);
            }
            _openButton.interactable = false;
        }

        private void RewardButtonHandler()
        {
            _model.ShowRewardButtonPressed?.Invoke(listIndex, _speechBubbleTransform);
        }

        private void OpenSet()
        {
            StatusUpdated.SafeInvoke();
            CardsModalButtonPressed.SafeInvoke(_model.Uid);
            if (!_model.Unlocked || _model.ComingSoon)
            {
                LockedButtonHandler();
            }
            else
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.Collection, DauInteractions.Collection.Set));
            }
        }

        [UsedImplicitly]
        public void OnTransitionSeen()
        {
            StateSeen.SafeInvoke(_model.Uid);
        }

        public void SetScrollView(LoopVerticalScrollRectMulti scrollView)
        {
            foreach (var touchHandler in _touchHandlers)
            {
                touchHandler.ScrollView = scrollView;
            }
        }

        private void PlayDelayedTransitionCoroutine(int animationParameter)
        {
            if (!gameObject.activeInHierarchy)
                return;

            _transitionCoroutine = StartCoroutine(PlayDelayedTransition(animationParameter));
        }

        private IEnumerator PlayDelayedTransition(int animationParameter)
        {
            var iconRect = _icon.RectTransform();
            while (!RectTransformUtility.RectangleContainsScreenPoint(MaskRect, iconRect.position))
                yield return null;
            _animator.SetBool(animationParameter, true);
        }

        private void InitItem(CollectionSetViewModel model)
        {
            UninitItem();
            _model = model;
            _grayscaleSwapper.StoreOriginalValues();

            _nameText.SetTextId(model.Name);
            if (model.Unlocked && !model.ComingSoon)
            {
                _grayscaleSwapper.SetValue(false);
                _icon.sprite = _model.GetIcon();
            }
            
            if (_transitionCoroutine != null)
                StopCoroutine(_transitionCoroutine);
            _transitionCoroutine = null;
           _animator.ResetAllParameters();
           if (_model.Claimed)
           {
               if (!_model.IsStateSeen)
               {
                   OnTransitionSeen();
                   _animator.SetTrigger(CompletedHash);
                   PlayDelayedTransitionCoroutine(ClaimHash);
               }
               else
                   _animator.SetTrigger(ClaimedHash);
           }
           else if (_model.Unlocked && !_model.ComingSoon)
           {
               _progressBar.SetProgress((float)model.CollectedCardsAmount / model.TotalCardsAmount);
               _progressText.text = $"{model.CollectedCardsAmount}/{model.TotalCardsAmount}";
               _reward.Setup(model.Reward);
               if (_model.Completed)
               {
                   _openButton.interactable = true;
                   if (!_model.IsStateSeen)
                   {
                       _animator.SetTrigger(UnlockedHash);
                       PlayDelayedTransitionCoroutine(CompleteHash);
                   }
                   else
                       _animator.SetTrigger(CompletedHash);
               }
               else
               {
                   if (!_model.IsStateSeen)
                   {
                        _lockedText.SetTextId(CollectionLockedSetLevel);
                        _lockedText.FormatSetArgs(_model.UnlockLevelName);
                        _animator.SetTrigger(LockedHash);
                        PlayDelayedTransitionCoroutine(UnlockHash);
                   }
                   else
                       _animator.SetTrigger(UnlockedHash);
               }
           }
           else
           {
               _grayscaleSwapper.SetValue(true);
               if (!model.ComingSoon)
               {
                   _lockedText.SetTextId(CollectionLockedSetLevel);
                   _lockedText.FormatSetArgs(_model.UnlockLevelName);
               }
               else
               {
                   _lockedText.SetTextId(ComingSoonButtonTitle);
               }

               _animator.SetTrigger(LockedHash);
           }

            StatusUpdated.SafeInvoke();
        }

        public override void OnInit(int index, object itemModel)
        {
            base.OnInit(index, itemModel);
            InitItem((CollectionSetViewModel)itemModel);
        }

        public void InitItem(int itemId)
        {
        }

        public void UninitItem()
        {
            _icon.sprite = null;
            _model = null;

            if (_transitionCoroutine != null)
            {
                StopCoroutine(_transitionCoroutine);
                _transitionCoroutine = null;
            }
        }
        
        public int GetStatus()
        {
            return _model?.NewCardsAmount ?? 0;
        }

        public int GetRewardStatus()
        {
            return 0;
        }

        public void ResetNotifiersActions()
        {
            
        }
    }
}