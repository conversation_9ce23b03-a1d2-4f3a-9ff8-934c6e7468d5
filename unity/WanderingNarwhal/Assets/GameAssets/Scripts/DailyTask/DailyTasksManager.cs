using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Modals;
using BBB.Wallet;
using BebopBee.Core.UI;
using Core.Configs;
using DG.Tweening;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.GenericModals.Info;
using GameAssets.Scripts.GenericModals.Info.Core;
using PBGame;
using Util = BBB.Util;

namespace GameAssets.Scripts.DailyTask
{
    public class DailyTasksManager : IDailyTasksManager, IContextInitializable, IContextReleasable
    {
        public event Action TasksUpdated;

        private static readonly Type[] RequiredConfigs =
        {
            typeof(DailyTasksConfig),
            typeof(DailyTaskSettingsConfig)
        };

        private const string DailyTasksLockUid = "daily_tasks";
        private const int FirstDayOfStreak = 1;

        public static int ActiveTasks { get; private set; } = 6;
        public static int VisibleTasks => ActiveTasks / 2;
        public static int HistoryDaysDefaultValue { get; private set; } = 2;
        public static int Streak { get; private set; } = 7;
        public int GetLocalNotificationTimeOffsetInSeconds => _settingsConfig.LocalNotifierTimeOffsetInMinutes * 60;
        public List<DailyTasksLocalNotifierSettings> NotifierSettingsList { get; } = new();

        private readonly Dictionary<int, List<string>> _categoriesByDay = new();
        private readonly Dictionary<int, HashSet<string>> _tasksByDay = new();

        private IDictionary<string, DailyTasksConfigT> _taskConfigs;
        private DailyTaskSettingsConfig _settingsConfig;
        private Dictionary<string, int> _historyDaysPerType = new();

        private ILockManager _lockManager;
        private IPlayerManager _playerManager;
        private IModalsBuilder _modalsBuilder;
        private TimeManager _timeManager;
        private IUIWalletManager _uiWalletManager;
        private IWalletManager _walletManager;
        private GameNotificationManager _notificationManager;

        private Tweener _autoRefreshTweener;
        private bool _isStarted;
        private bool _completedTaskSeen = true;
        private bool _shownInSession;

        private DailyTaskState TasksState => _playerManager.Player.DailyTaskState;
        public int CurrentStreakDay => TasksState.CurrentStreak;
        public bool JustShown { get; private set; }

        public void ResetTracking()
        {
            JustShown = false;
        }

        public bool IsAnyTaskCompleted
        {
            get
            {
                if (!_isStarted) return false;
                for (var i = 0; i < VisibleTasks; i++)
                {
                    var task = TasksState.Tasks[i];
                    if (IsTaskCompleted(task))
                    {
                        return true;
                    }
                }

                return false;
            }
        }

        private readonly Dictionary<string, TaskProgressTracker> _taskProgressTrackersByTaskType = new();

        public void InitializeByContext(IContext context)
        {
            _lockManager = context.Resolve<ILockManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _timeManager = context.Resolve<TimeManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();

            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();

            var config = context.Resolve<IConfig>();
            InitTaskTrackers(context);
            ConfigUpdatedHandler(config);
            
            context.Resolve<IModalsManager>().AddToInterruptionTracking(this);
        }

        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();

            foreach (var taskProgressTracker in _taskProgressTrackersByTaskType.Values)
            {
                taskProgressTracker.DeInit();
            }

            _taskProgressTrackersByTaskType.Clear();
        }

        public void Setup()
        {
            RefreshCycle();
            UpdateTaskTrackerProgresses();
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            // as confirmed with product we update tasks only when new day starts, without touching current
            Config.OnConfigUpdated += ConfigUpdatedHandler;

            if (!_notificationManager.IsInitialized)
            {
                _notificationManager.OnInitialized += NotifierInitializedHandler;
            }
            else
            {
                UpdateNotifier();
            }
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= ConfigUpdatedHandler;

            if (_notificationManager != null)
            {
                _notificationManager.OnInitialized -= NotifierInitializedHandler;
            }
        }

        private void InitLocalNotifierSettings()
        {
            NotifierSettingsList.Clear();
            for (var i = 0; i < _settingsConfig.LocalNotifierSettingsLength; i++)
            {
                NotifierSettingsList.Add(_settingsConfig.LocalNotifierSettings(i).GetValueOrDefault());
            }
        }

        private void NotifierInitializedHandler()
        {
            _notificationManager.OnInitialized -= NotifierInitializedHandler;
            UpdateNotifier();
        }

        private void UpdateNotifier()
        {
            if (!_notificationManager.IsInitialized)
                return;

            var tasksNotifier = _notificationManager.GetDailyTaskNotifier();
            var completedTasks = 0;
            var claimedTasks = 0;

            if (_isStarted)
            {
                for (var i = 0; i < VisibleTasks; i++)
                {
                    var task = TasksState.Tasks[i];
                    if (IsTaskCompleted(task))
                    {
                        if (!task.IsClaimed)
                        {
                            completedTasks++;
                        }
                        else
                        {
                            claimedTasks++;
                        }
                    }
                }
            }

            if (completedTasks > 0)
            {
                tasksNotifier.SetNotifier(1, completedTasks);
            }
            else
            {
                if (!_shownInSession && VisibleTasks != claimedTasks)
                {
                    tasksNotifier.SetNotifier(1);
                }
                else
                {
                    tasksNotifier.ResetNotifier();
                }
            }
        }

        private void InitTaskTrackers(IContext context)
        {
            _taskProgressTrackersByTaskType.Clear();

            // TODO add implementation of tasks trackers here
            RegisterTaskTracker(new CollectionCardTaskProgressTracker());
            RegisterTaskTracker(new DailyTriviaTaskProgressTracker());
            RegisterTaskTracker(new LevelWinTaskProgressTracker());
            RegisterTaskTracker(new BlueTileCollectedTaskProgressTracker());
            RegisterTaskTracker(new RedTileCollectedTaskProgressTracker());
            RegisterTaskTracker(new YellowTileCollectedTaskProgressTracker());
            RegisterTaskTracker(new HammerBoosterUsedTaskProgressTracker());
            RegisterTaskTracker(new BiplaneBoosterUsedTaskProgressTracker());
            RegisterTaskTracker(new SpaceshipBoosterUsedTaskProgressTracker());
            RegisterTaskTracker(new BombPowerupCreatedTaskProgressTracker());
            RegisterTaskTracker(new RocketPowerupCreatedTaskProgressTracker());
            RegisterTaskTracker(new ColorBombPowerupCreatedTaskProgressTracker());
            RegisterTaskTracker(new PropellerPowerupCreatedTaskProgressTracker());
            RegisterTaskTracker(new EpisodicTaskCompletedTaskProgressTracker());
            RegisterTaskTracker(new IapPurchasedTaskProgressTracker());

            foreach (var taskProgressTracker in _taskProgressTrackersByTaskType)
            {
                taskProgressTracker.Value.Init(context);
                taskProgressTracker.Value.Subscribe(TaskTrackerProgressHandler);
            }
            return;

            void RegisterTaskTracker(TaskProgressTracker taskProgressTracker)
            {
                _taskProgressTrackersByTaskType[taskProgressTracker.TaskType] = taskProgressTracker;
            }
        }

        private void UpdateTaskTrackerProgresses()
        {
            // when we initialize first time, to recover progress of previous actual tasks, we can rely on current tasks list
            // if it is other day, it will be reset to 0, otherwise we will continue from where we started for all 7 types
            if (!TasksState.Tasks.IsNullOrEmpty())
            {
                foreach (var taskState in TasksState.Tasks)
                {
                    if (_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
                    {
                        var taskType = dailyTaskConfig.Type;
                        if (_taskProgressTrackersByTaskType.TryGetValue(taskType, out var taskProgressTracker))
                        {
                            taskProgressTracker.SetProgress(taskState.Progress);
                        }
                        else
                        {
                            BDebug.Log(LogCat.Config, $"Couldn't find task progress tracker with task type {taskType}");
                        }
                    }
                    else
                    {
                        BDebug.Log(LogCat.Config, $"Couldn't find task config with uid {taskState.TaskUid}");
                    }
                }
            }
        }

        private void TaskTrackerProgressHandler(string taskType, int progress)
        {
            if (TasksState.Tasks.IsNullOrEmpty())
                return;

            bool shouldUpdateNotifier = false;
            for (int i = 0; i < TasksState.Tasks.Count; i++)
            {
                var taskState = TasksState.Tasks[i];
                if (_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
                {
                    if (dailyTaskConfig.Type == taskType)
                    {
                        var wasCompleted = IsTaskCompleted(taskState);
                        taskState.Progress = progress;
                        if (!wasCompleted && IsTaskCompleted(taskState))
                        {
                            if (i < VisibleTasks)
                            {
                                shouldUpdateNotifier = true;
                                _completedTaskSeen = false;
                            }

                            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.DailyTasks.Name,
                                DauInteractions.DailyTasks.CompleteTask, taskState.TaskUid));
                        }
                    }
                }
                else
                {
                    BDebug.Log(LogCat.Config, $"Couldn't find task config with uid {taskState.TaskUid}");
                }
            }

            if (shouldUpdateNotifier)
            {
                UpdateNotifier();
            }
        }

        private void ConfigUpdatedHandler(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _settingsConfig = config.TryGetDefaultFromDictionary<DailyTaskSettingsConfig>();
            if (_settingsConfig.ActiveTasks > 0)
            {
                ActiveTasks = _settingsConfig.ActiveTasks;
            }

            if (_settingsConfig.HistoryDays > 0)
            {
                HistoryDaysDefaultValue = _settingsConfig.HistoryDays;
            }

            if (_settingsConfig.HistoryDaysPerTypeLength > 0)
            {
                FlatBufferHelper.ToDict(_settingsConfig.HistoryDaysPerType, _settingsConfig.HistoryDaysPerTypeLength, _historyDaysPerType);
            }
            if (_settingsConfig.Streak > 0)
            {
                Streak = _settingsConfig.Streak;
            }

            var categories = ConfigUtils.ToListOfList(FlatBufferHelper.ToList(_settingsConfig.Categories, _settingsConfig.CategoriesLength));
            _categoriesByDay.Clear();
            // intentional reindexing based on human readable configs
            for (var i = 1; i <= categories.Count; i++)
            {
                _categoriesByDay.Add(i, categories[i - 1]);
            }
            _tasksByDay.Clear();
            _taskConfigs = config.Get<DailyTasksConfigT>();
            foreach (var dailyTaskConfig in _taskConfigs.Values)
            {
                _tasksByDay.TryAdd(dailyTaskConfig.Day, new HashSet<string>());
                _tasksByDay[dailyTaskConfig.Day].Add(dailyTaskConfig.Uid);
            }
            var existingCategories = new HashSet<string>();
            bool refreshTasks = false;
            var tasksToRemove = new List<TaskState>();
            foreach (var task in TasksState.Tasks)
            {
                _taskConfigs.TryGetValue(task.TaskUid, out var dailyTaskConfig);

                if (dailyTaskConfig != null)
                {
                    existingCategories.Add(dailyTaskConfig.Category);
                }
                else
                {
                    BDebug.Log(LogCat.Config, $"Couldn't find task config with uid {task.TaskUid}");
                    tasksToRemove.Add(task);
                    refreshTasks = true;
                }
            }
            foreach (var task in tasksToRemove)
            {
                TasksState.Tasks.Remove(task);
            }

            if (refreshTasks)
            {
                var day = TasksState.CurrentStreak;
                var categoriesForDay = new List<string>(_categoriesByDay[day]);
                categoriesForDay.RemoveAll(category => existingCategories.Contains(category));
                var taskUidsPool = new List<string>(_tasksByDay[day]);
                taskUidsPool.Shuffle();

                foreach (var category in categoriesForDay)
                {
                    DailyTasksConfigT newPickedTask = null;
                    foreach (var taskUid in taskUidsPool)
                    {
                        if (!_taskConfigs.TryGetValue(taskUid, out var taskConfig))
                            continue;

                        if (taskConfig.Category != category)
                            continue;

                        if (ShouldAvoidTask(taskConfig, day))
                            continue;

                        newPickedTask = taskConfig;
                        break;
                    }

                    if (newPickedTask != null)
                    {
                        taskUidsPool.Remove(newPickedTask.Uid);
                        TasksState.Tasks.Add(new TaskState()
                        {
                            TaskUid = newPickedTask.Uid,
                            IsClaimed = false,
                            IsSwapped = false,
                            Progress = 0,
                        });
                    }
                    else
                    {
                        BDebug.LogError(LogCat.General, $"No suitable task found for category {category}.");
                    }
                }
            }

            InitLocalNotifierSettings();
        }

        public bool ShouldAutoShowIntro(ScreenType currentScreen)
        {
            return ShouldShowHud(currentScreen) && !TasksState.IntroductionAlreadyShown;
        }

        public bool ShouldAutoShow(ScreenType currentScreen)
        {
            return ShouldShowHud(currentScreen) && !_completedTaskSeen;
        }

        public bool ShouldShowHud(ScreenType currentScreen)
        {
            return (currentScreen & ScreenType.FullHudScreen) > 0 && !_lockManager.IsLocked(DailyTasksLockUid, LockItemType.Other);
        }

        public void ShowModal(bool autoShow = false)
        {
            JustShown = true;
            _completedTaskSeen = true;
            if (!TasksState.IntroductionAlreadyShown)
            {
                if (autoShow)
                {
                    Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name,
                        DauInteractions.AutoPopups.DailyTasks, DauInteractions.AutoPopups.Intro));
                }

                ShowIntroModal();
            }
            else
            {
                if (autoShow)
                {
                    Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name,
                        DauInteractions.AutoPopups.DailyTasks, DauInteractions.AutoPopups.DailyTasks));
                }

                ShowDailyTasksModal();
            }
        }

        private void ShowIntroModal()
        {
            var dailyTasksIntroModel = new DailyTasksIntroModel();
            dailyTasksIntroModel.SetupHideCallback(CloseHandler);
            _modalsBuilder.CreateModalView<GenericInfoModalController>(ModalsType.GenericInfoModal).SetupAndShow(dailyTasksIntroModel);

            void CloseHandler()
            {
                TasksState.IntroductionAlreadyShown = true;
                ShowDailyTasksModal();
            }
        }

        private void ShowDailyTasksModal()
        {
            var dailyTasksController = _modalsBuilder.CreateModalView<DailyTasksController>(ModalsType.DailyTasksModal);
            dailyTasksController.ShowModal(ShowMode.Delayed);
        }

        private DateTime GetDayCycleStartTime()
        {
            var now = _timeManager.GetCurrentLocalDateTime();
            return now.Date;
        }

        private DateTime GetDayCycleEndTime()
        {
            // as confirmed, feature is cycling based on local midnight
            var now = _timeManager.GetCurrentLocalDateTime();
            return now.Date.AddDays(1);
        }

        public double GetTimeLeft()
        {
            var now = _timeManager.GetCurrentLocalDateTime();
            var endDateTime = GetDayCycleEndTime();
            return (endDateTime - now).TotalSeconds;
        }

        public void RefreshCycle()
        {
            if (_lockManager.IsLocked(DailyTasksLockUid, LockItemType.Other))
                return;

            _isStarted = true;

            var lastDayCycleStart = TasksState.LastDayCycleUtcTimestamp;
            var lastDayCycleStartDateTime = Util.UtcTimeStampToLocalDateTime(lastDayCycleStart);

            // if cycle start time >= it means that we are in current streak and ddo not need to update anything
            var currentDayCycleStartTime = GetDayCycleStartTime();
            if (lastDayCycleStartDateTime >= currentDayCycleStartTime && !TasksState.Tasks.IsNullOrEmpty())
                return;

            // resetting max streak on next day or starting when empty
            if (Streak == 0 || TasksState.Tasks.IsNullOrEmpty() || TasksState.CurrentStreak > Streak)
            {
                StartStreak();
                return;
            }

            // if last cycle activation was more than 24 hours ago, we reset the streak by time
            if ((currentDayCycleStartTime - lastDayCycleStartDateTime).Days > 0)
            {
                StartStreak();
            }
            else
            {
                // by design, check for progression on next day is claiming all
                if (AreAllTasksClaimed())
                {
                    ProgressStreak();
                }
                else
                {
                    StartStreak();
                }
            }
        }

        public void FlowRequested(string taskUid)
        {
            for (var i = 0; i < VisibleTasks; i++)
            {
                var taskState = TasksState.Tasks[i];
                if (taskState.TaskUid != taskUid)
                    continue;

                if (!_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
                    continue;

                var taskType = dailyTaskConfig.Type;
                if (_taskProgressTrackersByTaskType.TryGetValue(taskType, out var taskProgressTracker))
                {
                    taskProgressTracker.OnFlowRequested();
                }
            }
        }

        private void StartStreak()
        {
            TasksState.CurrentStreak = 1;
            UpdateCurrentDayTasks();
        }

        private void ProgressStreak()
        {
            UpdateCurrentDayTasks();
        }

        private void UpdateCurrentDayTasks()
        {
            _notificationManager.GetDailyTaskNotifier()?.ResetNotifier();
            _completedTaskSeen = true;

            // We need to reset progress on trakers whenever we start new set of tasks
            foreach (var taskTracker in _taskProgressTrackersByTaskType.Values)
            {
                taskTracker.ResetProgress();
            }

            TasksState.LastTasksTypesPicked ??= new List<LastTasksTypesPickedDto>();
            TasksState.LastDayCycleUtcTimestamp = _timeManager.CurrentTimeStamp();
            TasksState.StreakRewardClaimed = false;

            if (!TasksState.Tasks.IsNullOrEmpty())
            {
                var pickedTaskTypes = new List<string>();
                for (var i = 0; i < TasksState.Tasks.Count; i++)
                {
                    // we record as picked only the tasks player have seen
                    var taskState = TasksState.Tasks[i];
                    if (i < VisibleTasks || taskState.IsSwapped)
                    {
                        if (_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
                        {
                            pickedTaskTypes.Add(dailyTaskConfig.Type);
                        }
                    }
                }

                if (!pickedTaskTypes.IsNullOrEmpty())
                {
                    TasksState.LastTasksTypesPicked.Add(new LastTasksTypesPickedDto()
                    {
                        LastTasksTypesPicked = pickedTaskTypes,
                    });
                }
            }

            TasksState.Tasks ??= new List<TaskState>();
            TasksState.Tasks.Clear();

            var day = TasksState.CurrentStreak;
            var categories = _categoriesByDay[day];
            var taskUidsPool = new List<string>(_tasksByDay[day]);
            taskUidsPool.Shuffle();

            foreach (var category in categories)
            {
                DailyTasksConfigT pickedTask = null;
                foreach (var taskUid in taskUidsPool)
                {
                    if (!_taskConfigs.TryGetValue(taskUid, out var taskConfig))
                        continue;

                    if (taskConfig.Category != category)
                        continue;

                    if (ShouldAvoidTask(taskConfig, day))
                        continue;

                    pickedTask = taskConfig;
                    break;
                }

                if (pickedTask == null)
                {
                    var logLine = string.Empty;
                    foreach (var taskUid in taskUidsPool)
                    {
                        logLine += taskUid + ", ";
                    }

                    BDebug.Log(LogCat.Config, $"Couldn't find task with category: {category} from pool: {logLine}");

                    var pickedTaskUid = taskUidsPool.GetRandomItem();
                    if (!_taskConfigs.TryGetValue(pickedTaskUid, out var taskConfig))
                    {
                        BDebug.LogError(LogCat.Config, $"Couldn't find {pickedTaskUid} in task configs");
                        continue;
                    }

                    pickedTask = taskConfig;
                }

                taskUidsPool.Remove(pickedTask.Uid);
                TasksState.Tasks.Add(new TaskState()
                {
                    TaskUid = pickedTask.Uid,
                    IsClaimed = false,
                    IsSwapped = false,
                    Progress = 0,
                });
            }

            _playerManager.MarkDirty();
            TasksUpdated?.Invoke();
        }

        private bool ShouldAvoidTask(DailyTasksConfigT taskConfig, int day)
        {
            //if its FirstDayOfStreak then we can show same tasks again only if they are completable
            if (day == FirstDayOfStreak)
                return !_taskProgressTrackersByTaskType[taskConfig.Type].CheckIfTaskIsCompletable(taskConfig);

            var historyDays = _historyDaysPerType.Get(taskConfig.Type, HistoryDaysDefaultValue);
            if (historyDays == 0)
                return false;

            var historyDaysToCheck = Math.Max(0, TasksState.LastTasksTypesPicked.Count - historyDays);
            for (var i = TasksState.LastTasksTypesPicked.Count - 1; i >= historyDaysToCheck; i--)
            {
                if (TasksState.LastTasksTypesPicked[i].LastTasksTypesPicked.Contains(taskConfig.Type))
                    return true;
            }

            return !_taskProgressTrackersByTaskType[taskConfig.Type].CheckIfTaskIsCompletable(taskConfig);
        }

        public List<TaskState> GetCurrentTasks()
        {
            return TasksState.Tasks;
        }

        public bool TryClaimTask(string taskUid)
        {
            for (var i = 0; i < VisibleTasks; i++)
            {
                var taskState = TasksState.Tasks[i];
                if (taskState.TaskUid != taskUid)
                    continue;

                if (taskState.IsClaimed || !IsTaskCompleted(taskState))
                    return false;

                taskState.IsClaimed = true;

                if (_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
                {
                    var transaction = new Transaction()
                        .AddTag(TransactionTag.DailyTasks)
                        .SetAnalyticsData(CurrencyFlow.DailyTasks.Name, CurrencyFlow.DailyTasks.Task, taskState.TaskUid)
                        .Earn(FlatBufferHelper.ToDict(dailyTaskConfig.Reward));

                    _walletManager.TransactionController.MakeTransaction(transaction);
                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.DailyTasks);

                    Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.DailyTasks.Name,
                        DauInteractions.DailyTasks.ClaimTask, taskUid));
                }
                else
                {
                    BDebug.Log(LogCat.Config, $"Couldn't find task config with uid {taskState.TaskUid}");
                }

                //increase streak if all tasks claimed
                if (AreAllTasksClaimed())
                {
                    TasksState.CurrentStreak++;
                }

                _playerManager.MarkDirty();
                UpdateNotifier();

                return true;
            }

            BDebug.Log(LogCat.Config, $"Couldn't find task with uid {taskUid}");
            return false;
        }

        public bool TrySwapTask(string uid)
        {
            for (var i = 0; i < VisibleTasks; i++)
            {
                var taskState = TasksState.Tasks[i];
                if (taskState.TaskUid != uid)
                    continue;

                var taskStateToSwap = TasksState.Tasks[i + VisibleTasks];
                TasksState.Tasks[i] = taskStateToSwap;
                TasksState.Tasks[i + VisibleTasks] = taskState;

                // marking both as swapped to not complicate
                taskState.IsSwapped = true;
                taskStateToSwap.IsSwapped = true;

                _playerManager.MarkDirty();
                return true;
            }

            BDebug.Log(LogCat.Config, $"Couldn't find task with uid {uid}");
            return false;
        }

        public bool IsTaskCompleted(TaskState taskState)
        {
            if (_taskConfigs.TryGetValue(taskState.TaskUid, out var dailyTaskConfig))
            {
                return taskState.Progress >= dailyTaskConfig.Amount;
            }

            BDebug.Log(LogCat.Config, $"Couldn't find task config with uid {taskState.TaskUid}");
            return false;
        }

        public bool AreAllTasksClaimed()
        {
            if (TasksState.Tasks.IsNullOrEmpty())
                return false;

            //we checking for visible tasks only, we don't care about alternative/swapped
            for (var i = 0; i < MathF.Min(TasksState.Tasks.Count, VisibleTasks); i++)
            {
                var taskState = TasksState.Tasks[i];
                if (!taskState.IsClaimed)
                    return false;
            }

            return true;
        }

        public bool TryClaimStreakReward()
        {
            _shownInSession = true;
            UpdateNotifier();

            if (TasksState.StreakRewardClaimed)
                return false;

            if (!AreAllTasksClaimed())
                return false;

            TasksState.StreakRewardClaimed = true;
            _playerManager.MarkDirty();

            var completed = CurrentStreakDay > Streak;
            var reward = completed
                ? FlatBufferHelper.ToDict(_settingsConfig.CompleteStreakReward, _settingsConfig.CompleteStreakRewardLength)
                : FlatBufferHelper.ToDict(_settingsConfig.DayStreakReward, _settingsConfig.DayStreakRewardLength);

            if (reward.IsNullOrEmpty())
            {
                return false;
            }

            Analytics.LogEvent(completed
                ? new DauInteractionsEvent(DauInteractions.DailyTasks.Name, DauInteractions.DailyTasks.ClaimStreak,
                    string.Empty)
                : new DauInteractionsEvent(DauInteractions.DailyTasks.Name, DauInteractions.DailyTasks.ClaimDailyStreak,
                    CurrentStreakDay.ToString()));
            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
            var transaction = new Transaction()
                .AddTag(TransactionTag.DailyTasks)
                .SetAnalyticsData(CurrencyFlow.DailyTasks.Name, completed ? CurrencyFlow.DailyTasks.DailyStreak : CurrencyFlow.DailyTasks.CompletionStreak, CurrentStreakDay.ToString())
                .Earn(reward);

            rewardModal.SetupInitialParams(
                new CurrenciesRewardViewModel()
                {
                    RewardDict = reward,
                },
                skippedCurrencies =>
                {
                    _walletManager.TransactionController.MakeTransaction(transaction);
                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.DailyTasks, skippedCurrencies);
                });

            rewardModal.ShowModal();

            return true;
        }

        //Debug Tool Method
        public void SkipToNextDay()
        {
            TasksState.LastDayCycleUtcTimestamp -= 86400;
            RefreshCycle();
        }
    }
}