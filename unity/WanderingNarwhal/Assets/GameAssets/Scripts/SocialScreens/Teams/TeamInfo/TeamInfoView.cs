using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BBB.UI.Core;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.SocialScreens.Teams.Screens.LogoSelectionScreen;
using GameAssets.Scripts.UI.OverlayDialog;
using RPC.Teams;
using TMPro;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamInfo
{
    public class TeamInfoView : ContextedUiBehaviour
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private GameObject[] _rootHolder;

        [SerializeField] private TextMeshProUGUI _teamNameText;
        [SerializeField] private TextMeshProUGUI _teamDescriptionText;
        [SerializeField] private TextMeshProUGUI _teamMembersCountText;
        [SerializeField] private TextMeshProUGUI _teamScoreText;
        [SerializeField] private TextMeshProUGUI _teamRequiredLevelText;
        [SerializeField] private TextMeshProUGUI _teamLeaderNameText;
        [SerializeField] private LocalizedTextPro _teamActivityText;
        [SerializeField] private Color[] _teamActivityTextColor = new Color[3] {Color.red, Color.yellow, Color.green};
        [SerializeField] private LocalizedTextPro _teamTypeText;
        [SerializeField] private TeamLogoView _teamLogoView;

        [SerializeField] private GameObject _editButtonHolder;
        [SerializeField] private GameObject _joinButtonHolder;
        [SerializeField] private GameObject _leaveButtonHolder;
        [SerializeField] private GameObject _resignButtonHolder;
        [SerializeField] private Button _editButton;
        [SerializeField] private Button _joinButton;
        [SerializeField] private Button _leaveButton;
        [SerializeField] private Button _resignButton;

        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private Transform _membersRoot;
        [SerializeField] private GameObject _memberPrefab;
        [SerializeField] private TeamMemberContextSpeechBubble _teamMemberContextSpeechBubble;
        [SerializeField] private MaterialSwapper _materialSwapper;
        [SerializeField] private string _teamLevelRequireId;

        private IVibrationsWrapper _vibrations;

        private TextMeshProUGUI _teamActivityTextUgui;
        private TextMeshProUGUI TeamActivityTextUgui
        {
            get
            {
                if (_teamActivityTextUgui == null)
                {
                    _teamActivityTextUgui = _teamActivityText.gameObject.GetComponent<TextMeshProUGUI>();
                }

                return _teamActivityTextUgui;
            }
        }

        private TeamPublicInfo _teamPublicInfo;
        private Action _closeButtonCallback;
        private Action<TeamPublicInfo> _editButtonCallback;
        private Action<TeamPublicInfo, Transform> _joinButtonCallback;
        private Action<TeamPublicInfo> _leaveButtonCallback;
        private Action<TeamPublicInfo> _resignButtonCallback;
        private Action<TeamMemberInfo, bool> _kickCallback;
        private Action<TeamMemberInfo> _adminCallback;
        private Func<string, bool> _isLocalUser;
        private bool _isLeader;
        private bool _isOwner;
        private string _lastSelectedTeamMember;
        private Action<OverlayDialogConfig> _levelPromptCallback;

        public bool IsShown { get; private set; }

        protected override void Awake()
        {
            base.Awake();
            _closeButton.ReplaceOnClick(Close);
            _editButton.ReplaceOnClick(EditButtonHandler);
            _leaveButton.ReplaceOnClick(LeaveButtonHandler);
            _resignButton.ReplaceOnClick(ResignButtonHandler);

            _teamMemberContextSpeechBubble.Setup(AdminButtonHandler, KickButtonHandler);
            _materialSwapper.StoreOriginalValues();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            LazyInit();
        }

        private void AdminButtonHandler(TeamMemberInfo teamMemberInfo)
        {
            _adminCallback?.Invoke(teamMemberInfo);
        }

        private void KickButtonHandler(TeamMemberInfo teamMemberInfo, bool banUser)
        {
            _kickCallback?.Invoke(teamMemberInfo, banUser);
        }

        private void EditButtonHandler()
        {
            _editButtonCallback?.Invoke(_teamPublicInfo);
        }

        private void LeaveButtonHandler()
        {
            _leaveButtonCallback?.Invoke(_teamPublicInfo);
        }

        private void ResignButtonHandler()
        {
            _resignButtonCallback?.Invoke(_teamPublicInfo);
        }

        public void Setup(Func<string, bool> isLocalUser, Action closeButtonCallback, Action<TeamPublicInfo> editButtonCallback, Action<TeamPublicInfo, Transform> joinButtonCallback, Action<TeamPublicInfo> leaveButtonCallback, Action<TeamPublicInfo> resignButtonCallback,
            Action<TeamMemberInfo> adminCallback, Action<TeamMemberInfo, bool> kickCallback, Action<OverlayDialogConfig> levelPromptCallback)
        {
            _levelPromptCallback = levelPromptCallback;
            _isLocalUser = isLocalUser;

            _closeButtonCallback = closeButtonCallback;

            _editButtonCallback = editButtonCallback;
            _joinButtonCallback = joinButtonCallback;
            _leaveButtonCallback = leaveButtonCallback;
            _resignButtonCallback = resignButtonCallback;

            _adminCallback = adminCallback;
            _kickCallback = kickCallback;
        }

        public void ShowTeamInfo(TeamPublicInfo teamPublicInfo, bool isMember, bool hasRequiredLevel)
        {
            var myUser = teamPublicInfo.Members.Find(member => _isLocalUser(member.Uid));
            var owner = teamPublicInfo.Members.Find(member => member.IsOwner);
            _isLeader = myUser?.IsAdmin ?? false;
            _isOwner = myUser?.IsOwner ?? false;

            _teamPublicInfo = teamPublicInfo;
            _teamNameText.text = teamPublicInfo.Name;
            _teamDescriptionText.text = teamPublicInfo.Description;
            _teamMembersCountText.text = $"{teamPublicInfo.MembersNumber}/{teamPublicInfo.MembersLimit}";
            _teamScoreText.text = teamPublicInfo.Scores.ToString();
            _teamRequiredLevelText.text = teamPublicInfo.RequiredLevel.ToString();
            _teamActivityText.SetTextId(teamPublicInfo.GetActivityTextId());
            var teamActivity = (int) teamPublicInfo.Activity;
            TeamActivityTextUgui.color = teamActivity > _teamActivityTextColor.Length ? _teamActivityTextColor[^1] : _teamActivityTextColor[teamActivity];
            _teamTypeText.SetTextId(teamPublicInfo.GetTeamTypeTextId());
            _teamLogoView.Setup(teamPublicInfo.Thumbnail);
            _teamLeaderNameText.text = owner.Name;

            _rootHolder.Enable(true);

            _joinButtonHolder.SetActive(!isMember);
            _leaveButtonHolder.SetActive(isMember);
            _editButtonHolder.SetActive(_isLeader || _isOwner);
            _resignButtonHolder.SetActive(_isOwner && teamPublicInfo.MembersNumber > 1);//==1 is no one else to take ownership

            ShowMembers(teamPublicInfo);

            if (!isMember)
            {
                if (hasRequiredLevel)
                {
                    _materialSwapper.SetOriginalValue();
                }
                else
                {
                    _materialSwapper.SetNewValue();
                }
                _joinButton.ReplaceOnClick(() =>
                {
                    if (hasRequiredLevel)
                    {
                        _joinButtonCallback?.Invoke(_teamPublicInfo, _joinButton.transform);
                    }
                    else
                    {
                        OverlayDialogConfig overlayDialogConfig = new();
                        overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                        overlayDialogConfig.TargetTransform = _joinButton.transform;
                        overlayDialogConfig.ShowBackground = true;
                        overlayDialogConfig.TextToDisplay = _teamLevelRequireId;
                        overlayDialogConfig.TextArgs = new object[] { teamPublicInfo.RequiredLevel };
                        _levelPromptCallback?.Invoke(overlayDialogConfig);
                    }
                });
            }

            IsShown = true;
        }

        private void ShowMembers(TeamPublicInfo teamPublicInfo)
        {
            _membersRoot.RemoveAllActiveChilden();
            _scrollRect.verticalNormalizedPosition = 1f;

            var members = new List<TeamMemberData>(teamPublicInfo.Members);
            members.Sort((x, y) => y.Trophies.CompareTo(x.Trophies));

            var place = 0;
            foreach (var member in members)
            {
                place++;
                Profiler.BeginSample($"Instantiate[{_memberPrefab.name}]");
                var go = Instantiate(_memberPrefab, _membersRoot);
                Profiler.EndSample();
                var view = go.GetComponent<TeamMemberView>();
                var teamMemberInfo = new TeamMemberInfo();
                teamMemberInfo.SetupTeamMemberData(member);
                view.Setup(teamMemberInfo, place, true, _isLocalUser(member.Uid), MemberClickedHandler, !IsShown);

                if (_teamMemberContextSpeechBubble.IsShown && !_lastSelectedTeamMember.IsNullOrEmpty() && _lastSelectedTeamMember == member.Uid)
                {
                    MemberClickedHandler(view);
                }
            }
        }

        private void MemberClickedHandler(TeamMemberView teamMemberView)
        {
            _teamMemberContextSpeechBubble.Hide();

            if (_isLocalUser(teamMemberView.TeamMemberInfo.Uid))
                return;

            if (teamMemberView.TeamMemberInfo.IsOwner)
                return;
            
            if (!_isLeader && !_isOwner)
                return;
            
            if (teamMemberView.TeamMemberInfo.IsLeader && !_isOwner)
                return;

            _lastSelectedTeamMember = teamMemberView.TeamMemberInfo.Uid;
            _teamMemberContextSpeechBubble.ShowFor(teamMemberView);
        }

        private void Close()
        {
            BDebug.Log(LogCat.Vibration, $"Playing haptic feedback for Close button -- Vibrations available={_vibrations != null} -- Impact Type = {ImpactPreset.LightImpact} from TemaInfoView");
            _vibrations.PlayHaptic(ImpactPreset.LightImpact);
            Hide();
        }
        public void Hide()
        {
            var wasShown = IsShown;
            _rootHolder.Enable(false);
            IsShown = false;
            _teamMemberContextSpeechBubble.Hide();
            if (wasShown)
            {
                _closeButtonCallback?.Invoke();
            }
        }

        public void HideLeaderButtons()
        {
            _editButton.gameObject.SetActive(false);
            _resignButton.gameObject.SetActive(false);
        }
        
        protected override void InitWithContextInternal(IContext context)
        {
            _vibrations = context.Resolve<IVibrationsWrapper>();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _closeButtonCallback = null;
            _editButtonCallback = null;
            _joinButtonCallback = null;
            _leaveButtonCallback = null;
            _kickCallback = null;
            _adminCallback = null;
            _isLocalUser = null;
        }
    }
}