using System;
using BBB;
using FBConfig;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class IceBreakerQuestionMessageView : BbbMonoBehaviour
    {
        [SerializeField] private GameObject _holder;
        [SerializeField] private LocalizedTextPro _questionText;
        [SerializeField] private CollapsableMessageText _collapsableMessageText;
        [SerializeField] private VerticalLayoutGroup _verticalLayoutGroup;
        [SerializeField] private int _reactionPadding;
        [SerializeField] private int _normalPadding;

        public void Init(Action layoutRefreshCallback)
        {
            _holder.SetActive(false);
            _collapsableMessageText.Init(layoutRefreshCallback);
        }

        public void Setup(IceBreakerConfig iceBreakerConfig)
        {
            _questionText.SetTextId(iceBreakerConfig.Question);
            _collapsableMessageText.CheckCollapseState();
            _holder.SetActive(true);
        }
        
        public void UpdatePadding(bool hasReaction)
        {
            _verticalLayoutGroup.padding.top = hasReaction ? _reactionPadding : _normalPadding;
        }
    }
}