using System;
using System.Text.RegularExpressions;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.UI.Core;
using BebopBee;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.Social;
using BBB.Chat;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions;
using Kyub.EmojiSearch.UI;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using BBB.Social.Chat;
using FBConfig;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public class BaseMessageView : ContextedUiBehaviour, IMessageView, IPointerClickHandler
    {
        private const string MESSAGE_TIMESTAMP = "MESSAGE_TIMESTAMP";
        private const string GENERIC_TIME_MESSAGE_SENT_JUSTNOW = "GENERIC_TIME_MESSAGE_SENT_JUSTNOW";
        private static readonly Regex URLRegex = new(@"(((http|ftp|https):\/\/)?[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:\/~\+#]*[\w\-\@?^=%&amp;\/~\+#])?)",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private const string URL_REGEX_REPLACEMENT = "<color=#00CEFF><link=$1>$1</link></color>";
        private static readonly int Hide = Animator.StringToHash("Hide");

        public RectTransform EmojisAnchor => _emojisAnchor;
        public Canvas RootCanvas => _rootCanvas;

        [Header("Generic")]
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private RectTransform _root;
        [SerializeField] private RectTransform _emojisAnchor;
        [SerializeField] private Animator _animator;

        [SerializeField] private GameObject[] _senderInfoHolder;
        [SerializeField] private TextMeshProUGUI _name;
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private TextMeshProUGUI _timeStampText;

        [SerializeField] private Button _actionButton;
        [SerializeField] private MessageReactionsView _messageReactionsView;

        [Header("Text")]
        [SerializeField] private GameObject _messageTextHolder;
        [SerializeField] private TMP_EmojiTextUGUI _messageText;
        [SerializeField] private CollapsableMessageText _collapsableMessageText;
        [SerializeField] private IceBreakerQuestionMessageView _iceBreakerQuestionMessageView;

        [Header("Attachments")]
        [SerializeField] private GameObject _attachmentHolder;
        [SerializeField] private GameObject _imageHolder;
        [SerializeField] private Transform _imageTransformationRoot;
        [SerializeField] private GameObject _videoHolder;
        [SerializeField] private GameObject _gifHolder;

        [SerializeField] private AsyncImage _attachmentImage;
        [SerializeField] private TextMeshProUGUI _imageMetadata;
        [SerializeField] private AsyncImage _gifPreviewImage;
        [SerializeField] private AsyncImage _videoPreviewImage;
        [SerializeField] private Utils.AsyncGif _asyncGif;

        [SerializeField] private LayoutElement _resizableLayoutElement;
        [SerializeField] private RectTransform _sizeReferenceAnchor;
        [SerializeField] private float _referenceWidth;

        [SerializeField] private Button[] _attachmentButtons;

        [SerializeField] private GameObject[] _bgs;
        [SerializeField] private Color[] _textColors;

        [SerializeField] private GameObject _translatedMessageHolder;
        [SerializeField] private GameObject _translatedMessageTextHolder;
        [SerializeField] private GameObject _originalMessageTextHolder;
        [SerializeField] private Button _translateButton;

        [SerializeField] private LayoutTrigger _layoutTrigger;
        [SerializeField] private Transform _reactionsPanelRoot;
        [SerializeField] private Transform _reactionsPanelAnchor;

        private Action _refreshCallback;
        private Action<BaseMessageView> _clickCallback;
        private ILocalizationManager _localizationManager;
        private IChatManager _chatManager;

        private Action<ChatMessage> _attachmentClickCallback;

        private string _attachmentUrl;
        private bool _isLoading;
        private bool _isEmptyText;
        private bool _ignoreNextClick;

        private ChatMessageAttachmentType _attachmentType;
        private ChatMessageAttachement _attachment;
        private int _imageOrientation;
        private float _imageAspectRatio = 1;
        private bool _shouldUseTranslatedText = true;

        public ChatMessage Message { get; private set; }
        public RectTransform RectTransform => _root;

        public bool IsHeadless { get; private set; }
        public bool IsOwn { get; private set; }

        protected override void InitWithContextInternal(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _chatManager = context.Resolve<IChatManager>();

            _attachmentButtons.ReplaceOnClick(AttachmentClickHandler);
            _actionButton.ReplaceOnClick(ActionButtonHandler);
            _translateButton.ReplaceOnClick(TranslateButtonHandler);
            _collapsableMessageText.Init(LayoutRefreshHandler);
            _iceBreakerQuestionMessageView.Init(LayoutRefreshHandler);

            _imageMetadata.text = string.Empty;

            _attachmentImage.Image.color = Color.black;
            _videoPreviewImage.Image.color = Color.black;
            _gifPreviewImage.Image.color = Color.black;

            _layoutTrigger.LayoutTriggered -= RefreshReactionsPanel;
            _layoutTrigger.LayoutTriggered += RefreshReactionsPanel;
        }

        private void LayoutRefreshHandler()
        {
            _refreshCallback?.Invoke();
        }

        private void TranslateButtonHandler()
        {
            _shouldUseTranslatedText = !_shouldUseTranslatedText;
            SetupText(Message);
        }

        private void ActionButtonHandler()
        {
            if (_ignoreNextClick)
            {
                _ignoreNextClick = false;
                return;
            }

            _animator.SetTrigger("Pressed");
            _clickCallback?.Invoke(this);
        }

        private void AttachmentClickHandler()
        {
            _animator.SetTrigger("Pressed");
            _attachmentClickCallback?.Invoke(Message);
        }

        public void Setup(ChatMessage message, MessageType messageType, bool isHeadless, bool isOwn)
        {
            LazyInit();
            Message = message;
            IsHeadless = isHeadless;
            IsOwn = isOwn;
            _senderInfoHolder.Enable(!isHeadless);

            var colorIndex = messageType == MessageType.IceBreakerAnswer ? 1 : 0;
            _bgs.Enable(false);
            _bgs[colorIndex].SetActive(true);

            _messageText.color = _textColors[colorIndex];

            if (!isHeadless)
            {
                SetupSenderInfo(message);
            }

            UpdateView();

            SetupText(message);
            SetupAttachment(message);
            RefreshReactionsPanel();
        }

        public void UpdateView()
        {
            SetupTimestamp();
        }

        public void SetupAttachmentClickCallback(Action<ChatMessage> attachmentClickCallback)
        {
            _attachmentClickCallback = attachmentClickCallback;
        }

        public void SetupIceBreakerQuestion(IceBreakerConfig iceBreakerConfig)
        {
            _iceBreakerQuestionMessageView.Setup(iceBreakerConfig);
        }

        public void SetVisible(bool visible)
        {
            _rootCanvas.enabled = visible;
            if (visible)
                TryToLoad();

            if (_attachmentType == ChatMessageAttachmentType.Gif)
            {
                if (visible)
                    _asyncGif.Play();
                else
                    _asyncGif.Pause();
            }
        }

        public void SetConnectionState(bool connected)
        {
        }

        public void SetupReactions(ChatMessage message, bool reactionsUpdate = false)
        {
            Message = message;
            if (_messageReactionsView != null)
            {
                _messageReactionsView.Setup(message, reactionsUpdate, hasReactions =>
                {
                    _iceBreakerQuestionMessageView.UpdatePadding(hasReactions);
                });
            }
        }

        private void SetupTimestamp()
        {
            if (_timeStampText == null)
                return;

            var currentDateTimeOffset = Util.UnixUtcTimestamp();
            var difference = currentDateTimeOffset - Message.CreatedAt;

            if (difference >= 0)
            {
                var timeSpan = new TimeSpan(0, 0, (int)difference);
                string text;
                var wrapWithAgo = true;

                if (timeSpan.Days > 0)
                {
                    //N d N hr
                    if (timeSpan.Hours > 0)
                    {
                        text = _localizationManager.getLocalizedTextWithArgs(LocalizationTimeKeys.DaysHoursKey, timeSpan.Days, timeSpan.Hours);
                    } // N days 
                    else if (timeSpan.Days > 1)
                    {
                        text = _localizationManager.getLocalizedTextWithArgs(LocalizationTimeKeys.DayNumberKey, timeSpan.Days);
                    } // 1 day
                    else
                    {
                        text = _localizationManager.getLocalizedTextWithArgs(LocalizationTimeKeys.OneDayNumberKey, timeSpan.Days);
                    }
                }
                else if (timeSpan.Hours > 0) //00 h 00 m
                {
                    text = _localizationManager.getLocalizedTextWithArgs(LocalizationTimeKeys.HoursMinutesKey, timeSpan.Hours, timeSpan.Minutes);
                }
                else if (timeSpan.Minutes > 0)
                {
                    text = _localizationManager.getLocalizedTextWithArgs(LocalizationTimeKeys.MinutesKey, timeSpan.Minutes);
                }
                else
                {
                    text = _localizationManager.getLocalizedText(GENERIC_TIME_MESSAGE_SENT_JUSTNOW);
                    wrapWithAgo = false;
                }

                _timeStampText.text = wrapWithAgo ? _localizationManager.getLocalizedTextWithArgs(MESSAGE_TIMESTAMP, text) : text;

                _timeStampText.gameObject.SetActive(true);
            }
            else
            {
                Debug.LogError($"Couldn't define time: currentDateTimeOffset = {currentDateTimeOffset} , Message.CreatedAt = {Message.CreatedAt}");
                _timeStampText.gameObject.SetActive(false);
            }
        }

        private void SetupSenderInfo(ChatMessage message)
        {
            var senderName = "Missing Name";
            if (!message.Sender.Name.IsNullOrEmpty())
            {
                senderName = message.Sender.Name;
            }

            _asyncAvatar.Setup(new AvatarInfo(message.Sender.Pic));

            if (_name == null) return;

            var text = senderName;
            var country = message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.UserCountry);
            if (!country.IsNullOrEmpty())
            {
                var countryLocalized = _localizationManager.getLocalizedText("COUNTRY_" + country.ToUpperInvariant());
                text = _localizationManager.getLocalizedTextWithArgs("SOCIAL_MESSAGE_SENDER_NAME", senderName, countryLocalized);
            }

            _name.text = text;
        }

        private void SetupText(ChatMessage message)
        {
            var messageText = UpdateTranslatedText(message).Trim();

            _isEmptyText = messageText.IsNullOrEmpty();

            if (!_isEmptyText)
            {
                messageText = URLRegex.Replace(messageText, URL_REGEX_REPLACEMENT);
                _messageText.text = messageText;
                _collapsableMessageText.CheckCollapseState();
            }
            else
            {
                _messageText.text = string.Empty;
            }

            _messageTextHolder.SetActive(!_isEmptyText);
        }

        private string UpdateTranslatedText(ChatMessage message)
        {
            var messageText = message.Text.Trim();

            if (IsOwn)
            {
                _translatedMessageHolder.SetActive(false);
                return messageText;
            }

            var hasTranslation = message.I18n != null;
            var canSwitchTranslation = false;

            if (hasTranslation)
            {
                var languageKey = "language";
                if (message.I18n.ContainsKey(languageKey))
                {
                    var messageLanguage = message.I18n[languageKey];
                    if (message.I18n.ContainsKey(messageLanguage) && message.I18n.Values.Count > 2)
                    {
                        var currentLanguage = _chatManager.GetLanguage();
                        if (messageLanguage != currentLanguage)
                        {
                            var currentLanguageText = $"{currentLanguage}_text";
                            if (message.I18n.ContainsKey(currentLanguageText))
                            {
                                var translatedText = message.I18n[currentLanguageText].Trim();
                                // checking for equal to not show translation for numbers, emojis, non translatable text
                                if (!messageText.Equals(translatedText, StringComparison.OrdinalIgnoreCase))
                                {
                                    canSwitchTranslation = true;
                                    if (_shouldUseTranslatedText)
                                        messageText = translatedText;
                                }
                            }
                        }
                    }
                }
            }

            _translatedMessageHolder.SetActive(canSwitchTranslation);
            _translatedMessageTextHolder.SetActive(_shouldUseTranslatedText);
            _originalMessageTextHolder.SetActive(!_shouldUseTranslatedText);
            return messageText;
        }

        private void RefreshReactionsPanel()
        {
            _reactionsPanelRoot.position = _reactionsPanelAnchor.position;
        }

        private void SetupAttachment(ChatMessage message)
        {
            _attachmentHolder.SetActive(false);
            _imageHolder.SetActive(false);
            _videoHolder.SetActive(false);
            _gifHolder.SetActive(false);
            _imageTransformationRoot.localScale = Vector3.one;
            _imageTransformationRoot.localEulerAngles = Vector3.zero;

            _attachmentType = message.GetAttachmentType();
            if (_attachmentType == ChatMessageAttachmentType.None)
            {
                // force enable text so that at least empty message will be there
                if (_isEmptyText)
                {
                    _messageText.text = " ";
                    _messageTextHolder.SetActive(true);
                }

                return;
            }

            _attachment = message.Attachments[0];

            if (_attachment.OriginalWidth.HasValue && _attachment.OriginalHeight.HasValue)
            {
                var width = _attachment.OriginalWidth.Value;
                var height = _attachment.OriginalHeight.Value;

                _imageAspectRatio = (float)height / width;

                var downscale = width / Mathf.Min(width, _referenceWidth);
                width = Mathf.RoundToInt(width / downscale);
                height = Mathf.RoundToInt(height / downscale);
                _attachmentUrl += $"&w={width}&h={height}&resize=scale";
            }

            if (_attachmentType == ChatMessageAttachmentType.Image)
            {
                _attachmentUrl = message.Attachments[0].AssetUrl;
                _imageOrientation = _attachment.AdditionalProperties.GetIntProperty(ChatMessageAdditionalProperties.ImageOrientation);
                if (AppDefinesConverter.BbbDebug)
                    _imageMetadata.text = $"Orientation: {_imageOrientation}, {((NativeGallery.ImageOrientation)_imageOrientation).ToString()}";
                _imageHolder.SetActive(true);
            }
            else if (_attachmentType == ChatMessageAttachmentType.Video)
            {
                _attachmentUrl = message.Attachments[0].ThumbUrl;
                _videoHolder.SetActive(true);
            }
            else if (_attachmentType == ChatMessageAttachmentType.Gif)
            {
                _attachmentUrl = message.Attachments[0].ThumbUrl;
                _gifHolder.SetActive(true);
            }

            _attachmentHolder.SetActive(true);

            RefreshSize();
        }

        private void RefreshSize()
        {
            var desiredHeight = _referenceWidth * _imageAspectRatio;
            _resizableLayoutElement.preferredHeight = desiredHeight - _sizeReferenceAnchor.sizeDelta.y;
            _refreshCallback?.Invoke();
        }

        public void SetupClickCallback(Action<BaseMessageView> clickCallback)
        {
            _clickCallback = clickCallback;
        }

        public void SetupLayoutRefreshCallback(Action refreshCallback)
        {
            _refreshCallback = refreshCallback;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_attachmentImage != null)
            {
                _attachmentImage.Stop();
            }

            if (_layoutTrigger != null)
            {
                _layoutTrigger.LayoutTriggered -= RefreshReactionsPanel;
            }
        }

        public void HideAndDestroy()
        {
            if (_animator != null && gameObject.activeInHierarchy)
            {
                _animator.SetTrigger(Hide);
            }
            else
            {
                HideAnimationCallback();
            }
        }

        private void HideAnimationCallback()
        {
            Destroy(gameObject);
        }

        private void LogError(string error)
        {
            BDebug.LogError(LogCat.Social, error);
            _isLoading = false;
        }

        private void RetryLoad(string error)
        {
            _isLoading = false;
            switch (_attachmentType)
            {
                case ChatMessageAttachmentType.Image:
                    _attachmentImage.ResetNextLoadCooldown(_attachmentUrl);
                    break;
                case ChatMessageAttachmentType.Video:
                    _videoPreviewImage.ResetNextLoadCooldown(_attachmentUrl);
                    break;
                case ChatMessageAttachmentType.Gif:
                    _gifPreviewImage.ResetNextLoadCooldown(_attachmentUrl);
                    break;
            }

            TryToLoad(false);
        }

        private void TryToLoad(bool retryOnError = true)
        {
            if (_isLoading)
                return;

            Action<string> errorHandler = retryOnError ? RetryLoad : LogError;
            var attachmentUrlNotNullOrEmpty = !_attachmentUrl.IsNullOrEmpty();
            switch (_attachmentType)
            {
                case ChatMessageAttachmentType.Image when attachmentUrlNotNullOrEmpty:
                    _attachmentImage.Load(_attachmentUrl, SocialConstants.Attachments, errorHandler, sprite =>
                    {
                        SpriteUpdatedHandler(sprite);
                        _attachmentImage.Image.color = Color.white;
                    });

                    _isLoading = true;
                    break;
                case ChatMessageAttachmentType.Video when attachmentUrlNotNullOrEmpty:
                    _videoPreviewImage.Load(_attachmentUrl, SocialConstants.Attachments, errorHandler, sprite =>
                    {
                        SpriteUpdatedHandler(sprite);
                        _videoPreviewImage.Image.color = Color.white;
                    });

                    _isLoading = true;
                    break;
                case ChatMessageAttachmentType.Gif:
                {
                    if (attachmentUrlNotNullOrEmpty)
                    {
                        _gifPreviewImage.Load(_attachmentUrl, SocialConstants.Attachments, errorHandler, sprite =>
                        {
                            SpriteUpdatedHandler(sprite);
                            _gifPreviewImage.Image.color = Color.white;
                        });
                    }

                    _asyncGif.Load(_attachment.AssetUrl);
                    _isLoading = true;
                    break;
                }
            }
        }

        private void SpriteUpdatedHandler(Sprite sprite)
        {
            var width = sprite.rect.width;
            var height = sprite.rect.height;

            if (_imageOrientation >= 0)
            {
                var angleOfRotation = 360f - _imageOrientation * 90;
                _imageTransformationRoot.localEulerAngles = new Vector3(0f, 0f, angleOfRotation);

                if (_imageOrientation > 3)
                {
                    Debug.LogError($"Unknown orientation format: {((NativeGallery.ImageOrientation)_imageOrientation).ToString()}");
                }
                else
                {
                    if (_imageOrientation is 1 or 3)
                    {
                        _imageTransformationRoot.localScale = Vector3.one * (width / height);
                        height = width;
                        width = sprite.rect.height;
                    }
                }
            }

            _imageAspectRatio = height / width;

            RefreshSize();
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            var inputPosition = eventData.pressPosition;
            var linkIndex = TMP_TextUtilities.FindIntersectingLink(_messageText, inputPosition, null);

            if (linkIndex == -1)
                return;

            var linkInfo = _messageText.textInfo.linkInfo[linkIndex];
            var url = linkInfo.GetLinkID();

            var uri = new UriBuilder(url).Uri;
            Application.OpenURL(uri.ToString());

            _ignoreNextClick = true;
        }
    }
}