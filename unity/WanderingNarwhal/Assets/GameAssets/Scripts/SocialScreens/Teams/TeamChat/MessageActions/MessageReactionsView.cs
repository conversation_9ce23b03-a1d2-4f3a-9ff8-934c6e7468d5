using System;
using System.Collections.Generic;
using BBB;
using BBB.Social.Chat;
using UnityEngine;
using UnityEngine.Profiling;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions
{
    public class MessageReactionsView : BbbMonoBehaviour
    {
        [SerializeField] private ReactionsConfig _reactionsConfig;
        [SerializeField] private Transform _root;
        [SerializeField] private GameObject _reactionViewPrefab;

        private readonly Dictionary<string, MessageReactionView> _currentReactions = new();

        public void Setup(ChatMessage message, bool reactionsUpdate, Action<bool> hasReactionsCallback)
        {
            var reactionsToDelete = new List<string>(_currentReactions.Keys);

            foreach (var (reactionUid, reactionSenders) in message.Reactions)
            {
                if (reactionSenders == null || reactionSenders.Count == 0)
                    continue;

                if (_reactionsConfig.ReactionsDictionary.ContainsKey(reactionUid))
                {
                    reactionsToDelete.Remove(reactionUid);
                    if (_currentReactions.TryGetValue(reactionUid, out var currentReaction))
                    {
                        currentReaction.Setup(_reactionsConfig.ReactionsDictionary[reactionUid].ReactionIcon, reactionSenders.Count, false);
                    }
                    else
                    {
                        Profiler.BeginSample($"Instantiate[{_reactionViewPrefab.name}]");
                        var go = Instantiate(_reactionViewPrefab, _root);
                        Profiler.EndSample();
                        var view = go.GetComponent<MessageReactionView>();
                        view.Setup(_reactionsConfig.ReactionsDictionary[reactionUid].ReactionIcon, reactionSenders.Count, reactionsUpdate);

                        _currentReactions.Add(reactionUid, view);
                    }
                }
                else
                {
                    Debug.LogError($"Unsupported reaction type: {reactionUid}");
                }
            }

            foreach (var reactionKey in reactionsToDelete)
            {
                if (!_currentReactions.TryGetValue(reactionKey, out var reaction))
                    continue;

                Destroy(reaction.gameObject);
                _currentReactions.Remove(reactionKey);
            }
            
            hasReactionsCallback?.Invoke(_currentReactions.Count > 0);
        }
    }
}