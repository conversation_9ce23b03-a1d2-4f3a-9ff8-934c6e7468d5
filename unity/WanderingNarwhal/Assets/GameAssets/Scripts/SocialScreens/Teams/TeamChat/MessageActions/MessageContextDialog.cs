using System;
using BBB;
using BBB.Social.Chat;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions
{
    public class MessageContextDialog : BbbMonoBehaviour
    {
        private static readonly int ShowTrigger = Animator.StringToHash("Show");
        public bool IsShown { get; private set; }
        [SerializeField] private GameObject _overlay;
        [SerializeField] private ReactionsDialog _reactionsDialog;
        [SerializeField] private MessageActionsDialog _messageActionsDialog;

        [SerializeField] private AnimationEventHandler _aeh;
        [SerializeField] private Animator _animator;
        [SerializeField] private Button _closeButton;

        [SerializeField] private RectTransform _rootTransform;

        [SerializeField] private RectTransform _content;
        [SerializeField] private RectTransform _viewport;
        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private int _overrideCanvasSortingOrder;
        
        private int _defaultCanvasSortingOrder;
        
        public void SetOverrideCanvasSortingOrder(int overrideCanvasSortingOrder)
        {
            _rootCanvas.sortingOrder = overrideCanvasSortingOrder;
            _overrideCanvasSortingOrder = overrideCanvasSortingOrder + 1;
        }

        private BaseMessageView _messageView;
        private Action<ChatMessage, string> _reactionSelectedCallback;

        private void Awake()
        {
            _closeButton.ReplaceOnClick(AnimatedHide);
            _aeh.AnimationEvent -= AnimationEventHandler;
            _aeh.AnimationEvent += AnimationEventHandler;
            _defaultCanvasSortingOrder = _rootCanvas.sortingOrder;
        }

        public void Setup(BaseMessageView messageView, bool isLocal, Action<ChatMessage, string> reactionSelectedCallback, Action<ChatMessage> deleteMessageCallback, Action<ChatMessage> flagMessageCallback)
        {
            if (_messageView != null)
            {
                _messageView.RootCanvas.overrideSorting = false;
            }
            
            _messageView = messageView;
            _reactionSelectedCallback = reactionSelectedCallback;
            _reactionsDialog.Setup(messageView.Message, reactionSelectedCallback);
            _messageActionsDialog.Setup(messageView.Message, isLocal, new MessageActionsDialog.Callbacks()
                {
                    DeleteMessageCallback = message =>
                    {
                        deleteMessageCallback?.Invoke(message);
                        AnimatedHide();
                    },
                    FlagMessageCallback = message =>
                    {
                        flagMessageCallback?.Invoke(message);
                        AnimatedHide();
                    },
                }
            );
            
            _scrollRect.enabled = false;
        }

        public void UpdateMessage(ChatMessage message)
        {
            if (_messageView != null && _messageView.Message.Id == message.Id)
            {
                _reactionsDialog.Setup(message, _reactionSelectedCallback);
            }
        }

        public void Show()
        {
            _animator.SetTrigger(ShowTrigger);

            _overlay.SetActive(true);
            _messageActionsDialog.gameObject.SetActive(true);
            _reactionsDialog.gameObject.SetActive(true);

            RefreshPosition();
            IsShown = true;
        }

        public void Hide()
        {
            SetOverrideCanvasSortingOrder(_defaultCanvasSortingOrder);
            _overlay.SetActive(false);
            _messageActionsDialog.gameObject.SetActive(false);
            _reactionsDialog.gameObject.SetActive(false);

            if (_messageView != null)
            {
                _messageView.RootCanvas.overrideSorting = false;
            }
            _scrollRect.enabled = true;
            
            _messageView = null;
            IsShown = false;
        }

        public void RefreshPosition()
        {
            // here the clamp of emoji bar position happens based on bounds of other rect
            // what i need is to check the bounds of message relative to mask
            // move it up if it overlaps the bottom
            // move it down if it overlaps the top
            if (_messageView != null)
            {
                var contentPosition = _content.anchoredPosition.y;

                var messagePosition = _messageView.RectTransform().anchoredPosition.y;
                var messageHeight = _messageView.RectTransform().sizeDelta.y;

                var viewportHeight = _viewport.rect.height;

                var offsetOfEmojiBarFromBottom = 200;
                var offsetOfEmojiBarFromTop = 110;

                var contentPositionToAlignWithTop = -messagePosition - offsetOfEmojiBarFromTop;
                var contentPositionToAlignWithBottom = (-messagePosition) - viewportHeight + messageHeight + offsetOfEmojiBarFromBottom;
                var endPosition = Mathf.Clamp(contentPosition, contentPositionToAlignWithBottom, contentPositionToAlignWithTop);

                _messageView.RootCanvas.overrideSorting = true;
                _messageView.RootCanvas.sortingOrder = _overrideCanvasSortingOrder;

                if (Mathf.Abs(endPosition - contentPosition) > float.Epsilon)
                {
                    DOTween.To(() => 0f, (t) =>
                    {
                        _content.anchoredPosition = new Vector2(0f, contentPosition + (endPosition - contentPosition) * t);
                        _rootTransform.position = new Vector3(transform.position.x, _messageView.EmojisAnchor.transform.position.y, 0f);
                    }, 1f, 0.3f).SetEase(Ease.InOutSine);
                }
                else
                {
                    _rootTransform.position = new Vector3(transform.position.x, _messageView.EmojisAnchor.transform.position.y, 0f);
                }
            }
        }

        private void AnimationEventHandler()
        {
            Hide();
        }

        private void AnimatedHide()
        {
            _animator.SetTrigger("Hide");
        }
    }
}