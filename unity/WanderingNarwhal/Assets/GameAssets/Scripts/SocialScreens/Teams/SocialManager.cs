using System;
using System.Collections.Generic;
using BBB;
using BBB.BrainCloud;
using BBB.Chat;
using BBB.Core;
using BBB.Core.Analytics;
using BebopBee.Social;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Modals;
using BBB.Wallet;
using BebopBee;
using Bebopbee.Core.Systems.RpcCommandManager;
using Bebopbee.Core.Systems.RpcCommandManager.Chat;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam.CreateTeam;
using GameAssets.Scripts.SocialScreens.Teams.Screens;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using RPC.Teams;
using UnityEngine;
using UnityEngine.Profiling;
using BrainCloud;
using BBB.Social.Chat;
using BBB.TeamEvents;
using BebopBee.Core.UI;
using FBConfig;
using BBB.UI.Level;

namespace GameAssets.Scripts.SocialScreens.Teams
{
    public class SocialManager : IContextInitializable, ISocialManager, IContextReleasable
    {
        public Dictionary<string, long> CreateTeamPrice => new()
        {
            { WalletCurrencies.RegularCurrency, 100 },
        };


        public const string SOCIAL_UNLOCK_UID = "social";
        private const int HELP_REWARD_AMOUNT = 5;
        private const int AskForLivesCooldownFallback = 21600;
        private const int NudgeCooldownPeriodFallback = 604800;
        private const float TeamDataFetchPeriod = 60f;

        public event Action CurrentTeamFetched;
        public event Action CanAskLivesUpdated;

        private IAccountManager _accountManager;
        private IModalsBuilder _modalsBuilder;
        private SocialModalController _socialModalController;
        private ILockManager _lockManager;
        private IScreensManager _screensManager;
        private IModalsManager _modalsManager;
        private IPlayerManager _playerManager;
        private IUpdateDispatcher _updateDispatcher;
        private TimeManager _timeManager;
        private ILivesManager _livesManager;
        private GameNotificationManager _notificationManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private BrainCloudManager _brainCloudManager;
        private IChatManager _chatManager;
        private ITeamEventManager _teamEventManager;
        private IConfig _config;

        private float _lastTeamDataFetchTime;
        private bool _lastSocialModalOpeningFromTeamEvent;
        private string Uid => _brainCloudManager.ProfileId;

        private int? _askForLivesCooldownPeriod;
        private bool _forceCanAskForLives;
        public int AskForLivesCooldownPeriod
        {
            get
            {
                if (!_askForLivesCooldownPeriod.HasValue)
                {
                    var socialConfig = _config.TryGetDefaultFromDictionary<SocialConfig>();
                    return socialConfig.AskForLivesCooldown > 0 ? socialConfig.AskForLivesCooldown : AskForLivesCooldownFallback;
                }

                return _askForLivesCooldownPeriod.Value;
            }

            set { _askForLivesCooldownPeriod = value; }
        }

        private int NudgeCooldownPeriod
        {
            get
            {
                var socialConfig = _config.TryGetDefaultFromDictionary<SocialConfig>();
                return socialConfig.NudgeCooldown > 0 ? socialConfig.NudgeCooldown : NudgeCooldownPeriodFallback;
            }
        }

        public bool LastSocialModalOpeningFromTeamEvent => _lastSocialModalOpeningFromTeamEvent;

        public List<TeamMemberData> TeamMates
        {
            get
            {
                var teamMates = new List<TeamMemberData>();
                if (CurrentTeam == null) return teamMates;

                foreach (var teamMember in CurrentTeam.Members)
                {
                    if (teamMember.Uid != Uid)
                    {
                        teamMates.Add(teamMember);
                    }
                }

                return teamMates;
            }
        }

        public TeamData CurrentTeam => _accountManager?.Profile?.CurrentTeam;

        public List<TeamPublicInfo> BrowsedTeams { get; } = new();

        public void InitializeByContext(IContext context)
        {
            _lockManager = context.Resolve<ILockManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _modalsManager = context.Resolve<IModalsManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _updateDispatcher = context.Resolve<IUpdateDispatcher>();
            _timeManager = context.Resolve<TimeManager>();
            _livesManager = context.Resolve<ILivesManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _chatManager = context.Resolve<IChatManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _config = context.Resolve<IConfig>();

            Profiler.BeginSample("ScheduleTeamDataFetch");
            ScheduleTeamDataFetch();
            Profiler.EndSample();

            Subscribe();

            if (_brainCloudManager.IsAuthenticated)
            {
                BrowseTeams();
            }
        }

        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _updateDispatcher.OnUpdate += UpdateHandler;
            _chatManager.ConnectedToChatSuccess += ConnectedToChatHandler;
            _chatManager.UnreadMessagesUpdated += UnreadMessagesUpdatedHandler;
            _chatManager.UnreadIceBreakerAnswersUpdated += UnreadIceBreakerAnswersUpdatedHandler;
            _chatManager.KickedFromChat += OnKickedFromChat;

            _accountManager.Authenticated += BrowseTeams;
        }

        private void Unsubscribe()
        {
            _updateDispatcher.OnUpdate -= UpdateHandler;
            _chatManager.ConnectedToChatSuccess -= ConnectedToChatHandler;
            _chatManager.UnreadMessagesUpdated -= UnreadMessagesUpdatedHandler;
            _chatManager.UnreadIceBreakerAnswersUpdated -= UnreadIceBreakerAnswersUpdatedHandler;
            _chatManager.KickedFromChat -= OnKickedFromChat;
            _accountManager.Authenticated -= BrowseTeams;
        }

        private bool IsSocialModalShown()
        {
            return _modalsManager.IsShowingModal(typeof(SocialModalController));
        }

        private void UnreadMessagesUpdatedHandler(int unreadNumber)
        {
            if (IsSocialModalShown())
            {
                MarkAllAsRead();
                return;
            }

            var chatNotifier = _notificationManager.GetChatNotifier();
            chatNotifier?.SetNotifier(_accountManager.IsInTeam ? unreadNumber : 0);
        }
        
        private void UnreadIceBreakerAnswersUpdatedHandler(int unreadNumber)
        {
            var chatNotifier = _notificationManager.GetIceBreakerNotifier();
            //Adding unreadNumber as second parameter to make it a "green/reward notification"
            chatNotifier?.SetNotifier(_accountManager.IsInTeam ? unreadNumber : 0, unreadNumber);
        }

        private void OnKickedFromChat()
        {
            //update current team
            Profiler.BeginSample("FetchTeamData");
            FetchTeamData();
            Profiler.EndSample();
        }

        public void MarkAllAsRead()
        {
            var chatNotifier = _notificationManager.GetChatNotifier();
            chatNotifier.SetNotifier(0);

            _chatManager.MarkAllAsRead();
        }

        public void MarkAllIceBreakerAnswersAsRead()
        {
            var iceBreakerNotifier = _notificationManager.GetIceBreakerNotifier();
            iceBreakerNotifier.SetNotifier(0);
            _chatManager.MarkAllIceBreakerAnswersAsRead();
        }

        private void RefreshGifts()
        {
            _brainCloudManager.GetEntities(BCEntityTypes.Gift, GetEntitiesSuccess, GetEntitiesFailure);
            return;

            void GetEntitiesSuccess(BCEntity[] entities)
            {
                if (entities == null || entities.Length == 0)
                    return;

                var totalLivesNumber = 0;
                var giftType = HelpType.HelpTypeLife.ToString();
                var lastAppliedHelp = _playerManager.Player.LastAppliedGiftEntity.GetValueOrDefault(giftType);
                var appliedHelpTimestamp = lastAppliedHelp;

                var sortedEntities = (BCEntity[])entities.Clone();
                Array.Sort(sortedEntities, (e1, e2) => e1.CreatedAt.CompareTo(e2.CreatedAt));

                foreach (var entity in sortedEntities)
                {
                    var giftEntity = entity.Data?.ToObject<BCGiftEntityData>();
                    if (giftEntity == null || giftEntity.Value.GiftType != giftType)
                        continue;

                    _brainCloudManager.DeleteEntity(entity, null, null);
                    if (entity.CreatedAt <= lastAppliedHelp)
                        continue;

                    Analytics.LogEvent(new SocialEngagementEvent(Engagement.Social.Gift, Engagement.Social.Accept));

                    totalLivesNumber += giftEntity.Value.Amount;
                    appliedHelpTimestamp = entity.CreatedAt;
                }

                if (totalLivesNumber <= 0) return;

                _playerManager.Player.LastAppliedGiftEntity[giftType] = appliedHelpTimestamp;
                _playerManager.Player.PlayerDO.Stats.TotalHelpsReceivedCount += totalLivesNumber;
                _accountManager.Profile.HelpsReceived = _playerManager.Player.PlayerDO.Stats.TotalHelpsReceivedCount;
                _livesManager.AddLife(new LivesData
                {
                    Count = totalLivesNumber,
                    IsIgnoreCap = true,
                    Hidden = false,
                    Category = CurrencyFlow.Social.Name,
                    Family = CurrencyFlow.Social.Gifts.Name,
                    Item = CurrencyFlow.Social.Gifts.Accept,
                });
            }

            void GetEntitiesFailure()
            {
                Debug.LogWarning($"Failed to get {BCEntityTypes.Gift} entities");
            }
        }

        private void UpdateHandler()
        {
            if (Time.realtimeSinceStartup <= _lastTeamDataFetchTime + TeamDataFetchPeriod || CurrentTeam == null) return;

            Profiler.BeginSample("FetchTeamData");
            FetchTeamData();
            RefreshGifts();
            Profiler.EndSample();
        }

        private void ScheduleTeamDataFetch()
        {
            _lastTeamDataFetchTime = Time.realtimeSinceStartup;
        }

        public void SetForceCanAskForLives(bool forceCanAskForLives)
        {
            _forceCanAskForLives = forceCanAskForLives;
            CanAskLivesUpdated?.Invoke();
        }

        public bool CanAskForLives()
        {
            if (_forceCanAskForLives)
                return true;

            if (_livesManager.HasMaxLives)
                return false;

            return GetRemainingTimeToAskLives() <= 0;
        }

        public bool CanNudgeTeamCoop()
        {
            var teamEvent = _teamEventManager.GetHighestPriorityEvent() as TeamCoopEvent;
            if (teamEvent == null || !teamEvent.ShouldShowIcon() || !teamEvent.Joined || teamEvent.Finished || teamEvent.Completed)
                return false;

            return GetRemainingTimeToNudgeTeamEvent() <= 0;
        }

        public double GetRemainingTimeToAskLives()
        {
            var currentTime = _timeManager.CurrentTimeStamp();
            var lastAsk = _playerManager.Player.PlayerDO.LastAskHelpTimestamp;
            return lastAsk + AskForLivesCooldownPeriod - currentTime;
        }

        public double GetRemainingTimeToNudgeTeamEvent()
        {
            var currentTime = _timeManager.CurrentTimeStamp();
            var lastAsk = _playerManager.Player.PlayerDO.LastNudgeTimestamp;
            return lastAsk + NudgeCooldownPeriod - currentTime;
        }

        public void AdminPlayer(string teamUid, TeamMemberInfo teamMemberInfo, Action<bool, string> adminPlayerCallback)
        {
            _brainCloudManager.ToggleAdminPrivileges(teamUid, teamMemberInfo, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                adminPlayerCallback?.Invoke(false, teamMemberInfo.IsLeader ? SocialErrors.RemovePlayerAdminError : SocialErrors.MakePlayerAdminError);
            }

            void SuccessCallback(BCTeamData teamData)
            {
                _accountManager.SetTeamData(teamData.ToTeamData(), _lastSocialModalOpeningFromTeamEvent);
                CurrentTeamFetched?.Invoke();
                ScheduleTeamDataFetch();
                adminPlayerCallback?.Invoke(true, string.Empty);
            }
        }

        public void KickPlayer(string teamUid, TeamMemberInfo teamMemberInfo, bool banUser, Action<bool, string> kickPlayerCallback)
        {
            _brainCloudManager.KickPlayerFromGroup(teamUid, teamMemberInfo, banUser, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                kickPlayerCallback?.Invoke(false, SocialErrors.KickPlayerError);
            }

            void SuccessCallback(BCTeamData teamData)
            {
                _accountManager.SetTeamData(teamData.ToTeamData(), _lastSocialModalOpeningFromTeamEvent);
                CurrentTeamFetched?.Invoke();
                ScheduleTeamDataFetch();
                kickPlayerCallback?.Invoke(true, string.Empty);
            }
        }

        private void ConnectedToChatHandler()
        {
            UpdateLastAskHelpTimeIfNeeded();
        }

        private void UpdateLastAskHelpTimeIfNeeded()
        {
            if (GetRemainingTimeToAskLives() > 0)
            {
                return;
            }

            var lastAskSaved = _playerManager.Player.PlayerDO.LastAskHelpTimestamp;
            var lastAsk = lastAskSaved;
            foreach (var message in _chatManager.Messages)
            {
                if (message.Value.CreatedAt > lastAsk && message.Value.GetMessageType() == MessageType.AskForLives && _chatManager.IsLocalUser(message.Value.Sender.Id))
                {
                    lastAsk = message.Value.CreatedAt;
                }
            }

            if (lastAsk == lastAskSaved) return;

            _playerManager.Player.PlayerDO.LastAskHelpTimestamp = lastAsk;
            _playerManager.MarkDirty(true);
            
            CanAskLivesUpdated?.Invoke();
        }

        public void AskForLives<T>(Action<bool, T> askForLivesCallback, string localMessageId)
        {
            if (!CanAskForLives())
            {
                askForLivesCallback?.Invoke(false, default(T));
                return;
            }

            // one time enforcement till click
            SetForceCanAskForLives(false);
            Analytics.LogEvent(new SocialEngagementEvent(Engagement.Social.Gift, Engagement.Social.Ask));
            var curTime = _timeManager.CurrentTimeStamp();

            // Update timestamp immediately to prevent spam clicks
            var previousTimestamp = _playerManager.Player.PlayerDO.LastAskHelpTimestamp;
            _playerManager.Player.PlayerDO.LastAskHelpTimestamp = curTime;
            _playerManager.MarkDirty(true);
            CanAskLivesUpdated?.Invoke();

            ProcessAskForLivesServerLogic<T>((long)curTime, SuccessCallback, FailureCallback, localMessageId);
            return;

            void FailureCallback()
            {
                // Reset timestamp on failure so player can try again
                _playerManager.Player.PlayerDO.LastAskHelpTimestamp = previousTimestamp;
                _playerManager.MarkDirty(true);
                CanAskLivesUpdated?.Invoke();
                askForLivesCallback?.Invoke(false, default(T));
            }

            void SuccessCallback(T responseData)
            {
                // Timestamp is already set, just handle the success logic
                if (!_livesManager.CanPlayLevel)
                {
                    _livesManager.AddLife(new LivesData
                    {
                        Count = 1,
                        IsIgnoreCap = true,
                        Hidden = false,
                        Category = CurrencyFlow.Social.Name,
                        Family = CurrencyFlow.Social.Gifts.Name,
                        Item = CurrencyFlow.Social.Gifts.FreeGift
                    });

                    Analytics.LogEvent(new SocialEngagementEvent(Engagement.Social.Gift, Engagement.Social.FreeGift));
                }

                askForLivesCallback?.Invoke(true, responseData);
            }
        }

        private void ProcessAskForLivesServerLogic<T>(long timestamp, Action<T> successCallback, Action failureCallback, string localMessageId = null)
        {
            // Create the actual chat message request to send to server
            var chatMessageRequest = new ChatMessageRequest()
            {
                MessageType = MessageType.AskForLives.ToString(),
                Text = $"AskForLivesMessage from {_accountManager.Profile.Name}",
                AdditionalProperties = new Dictionary<string, object>()
                {
                    { "localMessageId", localMessageId } // Send the local message ID
                }
            };

            _chatManager.SendMessage(chatMessageRequest, SendMessageSuccess, SendMessageFailure);
            return;

            void SendMessageSuccess()
            {
                successCallback?.Invoke(default(T));
            }

            void SendMessageFailure()
            {
                failureCallback?.Invoke();
            }
        }

        public void SendHelp(ChatMessage message, Action<bool, string> sendHelpCallback)
        {
            Analytics.LogEvent(new SocialEngagementEvent(Engagement.Social.Gift, Engagement.Social.Help));

            _brainCloudManager.SendHelp(message, HelpType.HelpTypeLife.ToString(), 1, SendHelpSuccess, SendHelpFailure);
            return;

            void SendHelpFailure(BCSendHelpError error)
            {
                switch (error)
                {
                    case BCSendHelpError.MemberDoesntExists:
                    case BCSendHelpError.NotInTeam:
                        SendHelpSuccess();
                        break;
                    default:
                        sendHelpCallback?.Invoke(false, $"Exception on SendHelp: {error.ToString()}");
                        break;
                }

                sendHelpCallback?.Invoke(false, SocialErrors.SendHelpError);
            }

            void SendHelpSuccess()
            {
                var transaction = new Transaction()
                    .SetAnalyticsData(CurrencyFlow.Social.Name, CurrencyFlow.Social.Help, string.Empty)
                    .Earn(WalletCurrencies.RegularCurrency, HELP_REWARD_AMOUNT)
                    .AddTag(TransactionTag.Teams);

                _walletManager.TransactionController.MakeTransaction(transaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Teams);

                _accountManager.IncrementHelpCount();
                _chatManager.SetLocalHelpSent(message);

                sendHelpCallback?.Invoke(true, string.Empty);
            }
        }

        public void ClaimIap(ChatMessage message, string productUid, Dictionary<string, int> reward, Action<bool, string> claimIapCallback)
        {
            _chatManager.ClaimIap(message, ClaimIapSuccess, ClaimIapFailure);
            return;

            void ClaimIapFailure()
            {
                claimIapCallback?.Invoke(false, string.Empty);
            }

            void ClaimIapSuccess()
            {
                var transaction = new Transaction()
                    .AddTag(TransactionTag.Teams)
                    .SetAnalyticsData(CurrencyFlow.Social.Name, CurrencyFlow.Social.SharedIAP, productUid)
                    .Earn(reward);

                _walletManager.TransactionController.MakeTransaction(transaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Teams);

                _chatManager.SetLocalIapClaimed(message);

                claimIapCallback?.Invoke(true, string.Empty);
            }
        }

        public void NudgeTeamCoop(Action<bool, string> nudgeTeamCoopCallback)
        {
            if (!CanNudgeTeamCoop())
            {
                nudgeTeamCoopCallback?.Invoke(false, SocialErrors.TeamsNudgeCooldownError);
                return;
            }

            Analytics.LogEvent(new SocialEngagementEvent(Engagement.Social.Gift, Engagement.Social.Nudge));

            Dictionary<string, object> additionalProperties = new();
            var teamEvent = _teamEventManager.GetHighestPriorityEvent();
            if (teamEvent != null)
            {
                additionalProperties.Add(ChatMessageAdditionalProperties.TeamEventUid, teamEvent.Uid);
            }

            var currentTimeStamp = _timeManager.CurrentTimeStamp();
            var chatMessageRequest = new ChatMessageRequest()
            {
                MessageType = MessageType.NudgeTeamEvent.ToString(),
                Text = $"NudgeTeamEventMessage from {MultiDevice.GetUserId()}",
                AdditionalProperties = additionalProperties,
            };

            _chatManager.SendMessage(chatMessageRequest, SuccessCallback, FailureCallback);
            return;

            void SuccessCallback()
            {
                _playerManager.Player.PlayerDO.LastNudgeTimestamp = currentTimeStamp;

                if (!_chatManager.IsChatConnected())
                {
                    _chatManager.AddLocalMessage(MessageType.NudgeTeamEvent);
                }

                nudgeTeamCoopCallback?.Invoke(true, string.Empty);
                _playerManager.MarkDirty(true);
            }

            void FailureCallback()
            {
                nudgeTeamCoopCallback?.Invoke(false, SocialErrors.TeamsNudgeError);
            }
        }

        private void BrowseTeams()
        {
            BrowseTeams(null);
        }

        public void BrowseTeams(Action<List<TeamPublicInfo>, bool, string> browseTeamsCallback)
        {
            var result = new List<TeamPublicInfo>();

            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId?.Trim();
            var highestPassedSortOrder = 0f;
            if (!string.IsNullOrEmpty(highestPassedLevel))
            {
                highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);
            }

            var avgLevelsPerDay = highestPassedSortOrder / (_timeManager.DaysSinceInstall + 1);
            _brainCloudManager.BrowseGroups(avgLevelsPerDay, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                browseTeamsCallback?.Invoke(result, false, SocialErrors.BrowseTeamsError);
            }

            void SuccessCallback(BCTeamData[] teamData)
            {
                foreach (var team in teamData)
                {
                    var teamPublicInfo = new TeamPublicInfo();
                    teamPublicInfo.SetupTeamData(team.ToTeamData());
                    result.Add(teamPublicInfo);
                }

                RefreshTeamsList(result);
                browseTeamsCallback?.Invoke(result, true, string.Empty);
            }
        }

        private void RefreshTeamsList(List<TeamPublicInfo> teams)
        {
            if (teams.Count == 0)
                return;

            BrowsedTeams.Clear();
            foreach (var teamPublicInfo in teams)
            {
                BrowsedTeams.Add(teamPublicInfo);
            }
        }

        public void SearchTeams(string searchQuery, Action<List<TeamPublicInfo>, bool, string> searchTeamsCallback)
        {
            var result = new List<TeamPublicInfo>();

            _brainCloudManager.SearchGroupsByName(searchQuery, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                searchTeamsCallback?.Invoke(result, false, SocialErrors.SearchTeamsError);
            }

            void SuccessCallback(BCTeamData[] teamData)
            {
                foreach (var team in teamData)
                {
                    var teamPublicInfo = new TeamPublicInfo();
                    teamPublicInfo.SetupTeamData(team.ToTeamData());
                    result.Add(teamPublicInfo);
                }

                searchTeamsCallback?.Invoke(result, true, string.Empty);
            }
        }

        public void CreateTeam(TeamCreationData teamCreationData, Action<bool, string> teamCreationCallback)
        {
            var country = _accountManager.Profile?.Country ?? ProfileUtils.DefaultCountry;

            _brainCloudManager.CreateGroup(teamCreationData.Name, teamCreationData.Description, teamCreationData.TeamType,
                teamCreationData.RequiredLevel, teamCreationData.Icon, country, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                teamCreationCallback?.Invoke(false, SocialErrors.CreateTeamError);
            }

            void SuccessCallback(BCTeamData teamData)
            {
                var data = teamData.ToTeamData();
                data.JoinedAt = BBB.Util.UnixUtcTimestamp();
                _accountManager.SetTeamData(data, _lastSocialModalOpeningFromTeamEvent);
                CurrentTeamFetched?.Invoke();
                ScheduleTeamDataFetch();
                teamCreationCallback?.Invoke(true, string.Empty);
            }
        }

        public void EditTeam(TeamCreationData teamCreationData, Action<bool, string> teamEditingCallback)
        {
            var country = !string.IsNullOrEmpty(CurrentTeam.Country) ? CurrentTeam.Country : _accountManager.Profile?.Country ?? ProfileUtils.DefaultCountry;

            _brainCloudManager.EditGroup(CurrentTeam.TeamUid, teamCreationData.Name, teamCreationData.Description, teamCreationData.TeamType,
                teamCreationData.RequiredLevel, teamCreationData.Icon, country, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                teamEditingCallback?.Invoke(false, SocialErrors.EditTeamError);
            }

            void SuccessCallback(BCTeamData teamData)
            {
                _accountManager.SetTeamData(teamData.ToTeamData(), _lastSocialModalOpeningFromTeamEvent);
                teamEditingCallback?.Invoke(true, string.Empty);
                CurrentTeamFetched?.Invoke();
                ScheduleTeamDataFetch();
            }
        }

        public void JoinTeam(string teamUid, Action<bool, string> joinTeamCallback)
        {
            _brainCloudManager.JoinGroup(teamUid, SuccessCallback, FailureCallback);
            return;

            void FailureCallback(int code)
            {
                switch (code)
                {
                    case ReasonCodes.GROUP_IS_FULL:
                        joinTeamCallback?.Invoke(false, SocialErrors.JoinTeamErrorFullTeam);
                        break;
                    case ReasonCodes.NOT_INVITED_GROUP_MEMBER:
                        joinTeamCallback?.Invoke(false, SocialErrors.JoinTeamErrorKickedFromTeam);
                        break;
                    default:
                        joinTeamCallback?.Invoke(false, SocialErrors.JoinTeamError);
                        break;
                }
            }

            void SuccessCallback(BCTeamData teamData)
            {
                var data = teamData.ToTeamData();
                data.JoinedAt = BBB.Util.UnixUtcTimestamp();
                _accountManager.SetTeamData(data, _lastSocialModalOpeningFromTeamEvent);
                CurrentTeamFetched?.Invoke();
                joinTeamCallback?.Invoke(true, string.Empty);
            }
        }

        public void LeaveTeam(string teamUid, Action<bool, string> leaveTeamCallback)
        {
            if (!_accountManager.IsInTeam)
            {
                leaveTeamCallback?.Invoke(false, SocialErrors.LeaveTeamNotInTeamError);
                return;
            }

            if (CurrentTeam.TeamUid != teamUid)
            {
                leaveTeamCallback?.Invoke(false, SocialErrors.LeaveTeamDifferentTeamError);
                Debug.LogError($"Attempt to leave different team from current: {CurrentTeam.TeamUid}, {teamUid}");
                return;
            }

            _brainCloudManager.LeaveGroup(teamUid, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                leaveTeamCallback?.Invoke(false, SocialErrors.LeaveTeamError);
            }

            void SuccessCallback()
            {
                _accountManager.ResetHelpCount();
                _accountManager.SetTeamData(null, _lastSocialModalOpeningFromTeamEvent);
                leaveTeamCallback?.Invoke(true, string.Empty);
            }
        }

        public void ResignTeam(string teamUid, Action<bool, string> resignCallback)
        {
            if (!_accountManager.IsInTeam)
            {
                resignCallback?.Invoke(false, SocialErrors.ResignNotInTeamError);
                return;
            }

            if (CurrentTeam.TeamUid != teamUid)
            {
                resignCallback?.Invoke(false, SocialErrors.ResignDifferentTeamError);
                BDebug.LogError(LogCat.Social, $"Attempt to resign owner in different team from current: {CurrentTeam.TeamUid}, {teamUid}");
                return;
            }
            
            _brainCloudManager.ResignOwnerGroup(teamUid, SuccessCallback, FailureCallback);
            return;

            void FailureCallback()
            {
                resignCallback?.Invoke(false, SocialErrors.ResignError);
            }

            void SuccessCallback(string newOwnerId)
            {
                CurrentTeam.Members.Find(member => member.IsOwner).IsOwner = false;
                CurrentTeam.Members.Find(member => member.Uid == newOwnerId).IsOwner = true;
                resignCallback?.Invoke(true, string.Empty);
            }
        }

        public void FetchTeamData()
        {
            if (CurrentTeam == null)
                return;

            _brainCloudManager.FetchTeamInfo(CurrentTeam.TeamUid, SuccessCallback, FailureCallback);
            ScheduleTeamDataFetch();
            return;

            void FailureCallback(int code)
            {
                switch (code)
                {
                    case ReasonCodes.GROUP_NOT_FOUND:
                    case ReasonCodes.MISSING_RECORD:
                    {
                        _accountManager.SetTeamData(null, _lastSocialModalOpeningFromTeamEvent);
                        break;
                    }
                }

                CurrentTeamFetched?.Invoke();
            }

            void SuccessCallback(BCTeamData teamData)
            {
                _accountManager.SetTeamData(teamData.ToTeamData(), _lastSocialModalOpeningFromTeamEvent);
                CurrentTeamFetched?.Invoke();
            }
        }

        public void ShowSocialModal(Action onHide = null, bool fromTeamEvent = false)
        {
            _lastSocialModalOpeningFromTeamEvent = fromTeamEvent;

            _socialModalController = _modalsBuilder.CreateModalView<SocialModalController>(ModalsType.Social);
            _socialModalController.Setup(onHide);
            _socialModalController.ShowModal(ShowMode.Delayed);
        }

        public void HideSocialModal()
        {
            _socialModalController?.Hide();
        }

        public bool IsSocialUnlocked()
        {
            return !_lockManager.IsLocked(SOCIAL_UNLOCK_UID, LockItemType.Social);
        }

        public bool ShouldShowSocialInOtherPlaces()
        {
            var currentScreenType = _screensManager.GetCurrentScreenType();
            return IsSocialUnlocked() && currentScreenType != ScreenType.LevelScreen;
        }

        public bool IsAllowedUpdateInboxFromServer()
        {
            var screenType = _screensManager.GetCurrentScreenType();
            var isPlayScreen = (screenType & ScreenType.Levels) > 0;
            return !isPlayScreen || _modalsManager.IsShowingAModal(true);
        }

        public bool IsTeamMate(string uid)
        {
            if (!_accountManager.IsInTeam || uid == Uid || CurrentTeam?.Members == null)
                return false;

            foreach (var member in CurrentTeam.Members)
            {
                if (member.Uid == uid)
                    return true;
            }

            return false;
        }


        public async void SendLoginChallenge(string qrCode)
        {
            try
            {
                await RpcCommands.SendAsync<ChatLoginChallengeCommand>(qrCode);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.Social,$"Failed to do period team data fetch {e.Message}");
            }
        }
    }
}
