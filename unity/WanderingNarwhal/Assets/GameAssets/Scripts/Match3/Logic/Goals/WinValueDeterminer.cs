using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using Core.Configs;
using PBConfig;

namespace GameAssets.Scripts.Match3.Logic
{
    public class WinValueDeterminer
    {
        private readonly IDictionary<string, LevelAssistWeightsConfig> _levelAssistConfigDict;

        private Dictionary<long, float> _selectedWeights = new();

        public WinValueDeterminer(IDictionary<string, LevelAssistWeightsConfig> configDict)
        {
            _levelAssistConfigDict = configDict;
        }

        public void SetupLevel(ILevel level)
        {
            var uid = level == null || level.Config.Equals(FlatBufferHelper.DefaultProgressionLevelConfig) ? string.Empty : level.Config.Uid;
            if (!string.IsNullOrEmpty(uid)
                && _levelAssistConfigDict != null
                && _levelAssistConfigDict.TryGetValue(uid, out var config)
                && config is {GoalAssistWeights: not null})
            {
                _selectedWeights = new Dictionary<long, float>();
                foreach (var kvp in config.GoalAssistWeights)
                {
                    if (Enum.TryParse(kvp.Key, out GoalType goalType))
                    {
                        _selectedWeights[(long) goalType] = kvp.Value;
                    }
                }
            }
            else
            {
                if (_levelAssistConfigDict == null)
                {
                    BDebug.LogError(LogCat.Match3, "LevelAssistConfig dictionary is null");
                }

                _selectedWeights = new Dictionary<long, float>();
                if (level == null) return;

                foreach (var key in AssistState.GetAssistKeys(level.Goals, level.Grid))
                {
                    _selectedWeights[key] = 1f;
                }
            }
        }

        public float GetWinValue(AssistState progress, AssistState original)
        {
            progress.RemoveNonPositiveValues();
            original.RemoveNonPositiveValues();

            var goalContributionSum = 0f;
            var weightSum = 0f;
            
            foreach (var kvp in original)
            {
                var progressValue = progress.GetValue(kvp.Key);
                var originalValue = kvp.Value;

                var weight = _selectedWeights.GetValueOrDefault(kvp.Key, 1f);

                goalContributionSum += weight * progressValue / originalValue;
                weightSum += weight;
            }

            if (weightSum > 0)
            {
                return goalContributionSum / weightSum;
            }

            BDebug.LogError(LogCat.Match3, "Weight sum is 0, which is not supposed to happen");
            return 1f;
        }

    }
}