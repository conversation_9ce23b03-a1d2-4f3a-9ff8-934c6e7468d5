using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public sealed class GoalActionSpecial : GoalActionBase, IGoalGroupOwner
    {
        private readonly GoalActionType _type;

        public override bool ShouldRenderScoresOnBoard => false;
        public override bool ShouldAnimateDidi => true;

        public GoalActionSpecial(Coords displayCoords, GoalActionType type, TileKinds tileKind) 
            : base(displayCoords)
        {
            _type = type;
            TileKind = tileKind;
        }

        public override int GetComboDelta()
        {
            return 0;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            Score = rules.GetSpecialScore(_type);
            goalState.Reduce(GoalType.Score, Score);
        }

        public IEnumerable<Coords> AffectedCoords
        {
            get
            {
                yield return DisplayCoords;
            }
        }
    }
}