using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public sealed class GoalActionBackgroundDamaged : GoalActionBase, IGoalGroupOwner
    {
        private readonly int _bgHpLeft;

        public IEnumerable<Coords> AffectedCoords
        {
            get
            {
                yield return DisplayCoords;
            }
        }

        public GoalActionBackgroundDamaged(Cell cell, int bgHpLeft) : base(cell.Coords)
        {
            _bgHpLeft = bgHpLeft;
        }
        public override int GetComboDelta()
        {
            return 0;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            Score = rules.BackgroundDamaged + _bgHpLeft == 0 ? rules.BackgroundRemoved : 0;
            goalState.Reduce(GoalType.Score, Score);
        }
    }
}