using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public sealed class GoalActionMatch : GoalActionBase, IGoalGroupOwner
    {
        private Match _match;

        public IEnumerable<Coords> AffectedCoords => _match.GetAllCoords();


        public GoalActionMatch(Coords displayCoords, Match match) : base(displayCoords)
        {
            _match = match;
            DisplayCoords = (_match.Length > 3 || _match.MatchType == MatchType.Square) ? displayCoords : match.GetCoords(1);
            TileKind = match.Kind;
        }

        public override int GetComboDelta()
        {
            return 1;
        }

        public override float ComboCountToMult(int comboCount)
        {
            var result = comboCount > 1 ? comboCount : 1;
            if (result > 6) result = 6;
            return result;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            Score = UnityEngine.Mathf.RoundToInt(comboMult * rules.NormalTile * _match.Length);
            goalState.Reduce(GoalType.Score, Score);
        }
    }
}