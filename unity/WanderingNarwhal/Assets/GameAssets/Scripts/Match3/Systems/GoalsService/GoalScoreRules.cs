using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Logic;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Generic;
using UnityEngine;

namespace BBB.Match3.Systems.GoalsService
{
    public sealed class GoalScoreRules : IContextInitializable, IContextReleasable
    {
        private static readonly Type[] RequiredConfigs = {
            typeof(GoalScoreConfig)
        };

        private DamageSource[] _damageFlags;
        private Stage _stage = Stage.Good;
        private GoalScoreRulesDto.ScoreMultipliersLocal _scoreMultipliers;
        private ScoreMultiplierProvider _scoreMultProvider;
        private GoalScoreRulesDto.BaseScoresLocal _baseScores;
        private List<TileStateHpScore> _tileStateHpDecreaseScores;
        private List<TileStateScore> _tileStateDestroyScores;
        private Dictionary<TileSpeciality, int> _tileSpecialityDestroyScores = new ()
        {
            { TileSpeciality.None, 0 }
        };
        private Dictionary<TileSpeciality, int> _tileSpecialityAdjacentDamageScores = new ()
        {
            { TileSpeciality.None, 0 }
        };
        private Dictionary<GoalActionType, int> _specialScores = new ()
        {
            { GoalActionType.None, 0 }
        };
        private Dictionary<DamageSource, int> _damageSourceBonusScores = new ()
        {
            { DamageSource.None, 0 }
        };

        public GoalScoreRules()
        {
            var damageSourceValues = Enum.GetValues(typeof(DamageSource));
            _damageFlags = new DamageSource[damageSourceValues.Length];

            var index = 0;
            foreach (var value in damageSourceValues)
            {
                _damageFlags[index++] = (DamageSource)value;
            }
        }

        public GoalScoreRulesDto ToDto()
        {
            var dto = new GoalScoreRulesDto
            {
                damageFlags = _damageFlags,
                stage = _stage,
                scoreMultipliers = new GoalScoreRulesDto.ScoreMultipliersDto(_scoreMultipliers),
                baseScores = new GoalScoreRulesDto.BaseScoresDto(_baseScores),
                tileStateHpDecreasesScores = new List<GoalScoreRulesDto.TileStateHpScoreDto>()
            };
            foreach (var item in _tileStateHpDecreaseScores)
            {
                dto.tileStateHpDecreasesScores.Add(new GoalScoreRulesDto.TileStateHpScoreDto(item.State, item.Score, item.Hp));
            }

            dto.tileStateDestroyScores = new List<GoalScoreRulesDto.TileStateHpScoreDto>();
            foreach (var item in _tileStateDestroyScores)
            {
                dto.tileStateDestroyScores.Add(new GoalScoreRulesDto.TileStateHpScoreDto(item.State, item.Score));
            }

            dto.tileSpecialityDestroyScores = new List<GoalScoreRulesDto.TileStateHpScoreDto>();
            foreach (var item in _tileSpecialityDestroyScores)
            {
                dto.tileSpecialityDestroyScores.Add(new GoalScoreRulesDto.TileStateHpScoreDto(item.Key, item.Value));
            }

            dto.specialScores = new List<GoalScoreRulesDto.TileStateHpScoreDto>();
            foreach (var item in _specialScores)
            {
                dto.specialScores.Add(new GoalScoreRulesDto.TileStateHpScoreDto(item.Key, item.Value));
            }

            dto.damageSourceBonusScores = new List<GoalScoreRulesDto.TileStateHpScoreDto>();
            foreach (var item in _damageSourceBonusScores)
            {
                dto.damageSourceBonusScores.Add(new GoalScoreRulesDto.TileStateHpScoreDto(item.Key, item.Value));
            }
            return dto;
        }

        public void FromDto(GoalScoreRulesDto dto)
        {
            _damageFlags = dto.damageFlags;
            _stage = dto.stage;
            _scoreMultipliers = dto.scoreMultipliers.ConvertFromDto();
            _baseScores = dto.baseScores.ConvertFromDto();
            _tileStateHpDecreaseScores.Clear();
            foreach (var item in dto.tileStateHpDecreasesScores)
            {
                _tileStateHpDecreaseScores.Add(new TileStateHpScore(){ State = (TileState)item.State, Score = item.Score, Hp = item.Hp }); 
            }

            _tileStateDestroyScores.Clear();
            if (dto.tileStateDestroyScores != null)
            {
                foreach (var item in dto.tileStateDestroyScores)
                {
                    _tileStateDestroyScores.Add(new TileStateScore() { State = (TileState)item.State, Score = item.Score });
                }
            }

            _tileSpecialityDestroyScores.Clear();
            if (dto.tileSpecialityDestroyScores != null)
            {
                foreach (var item in dto.tileSpecialityDestroyScores)
                {
                    _tileSpecialityDestroyScores[item.Speciality] = item.Score;
                }
            }

            _tileSpecialityAdjacentDamageScores.Clear();
            if (dto.tileSpecialityAdjacentDamageScores != null)
            {
                foreach (var item in dto.tileSpecialityAdjacentDamageScores)
                {
                    _tileSpecialityAdjacentDamageScores[item.Speciality] = item.Score;
                }
            }

            _specialScores.Clear();
            if (dto.specialScores != null)
            {
                foreach (var item in dto.specialScores)
                {
                    _specialScores[item.GoalAction] = item.Score;
                }
            }

            _damageSourceBonusScores.Clear();
            if (dto.damageSourceBonusScores != null)
            {
                foreach (var item in dto.damageSourceBonusScores)
                {
                    _damageSourceBonusScores[item.DamageSource] = item.Score;
                }
            }
        }

        public void InitializeByContext(IContext context)
        {
            _scoreMultProvider = context.Resolve<ScoreMultiplierProvider>();

            var config = context.Resolve<IConfig>();
            SetupGoalScoreConfig(config);
            Config.OnConfigUpdated -= SetupGoalScoreConfig;
            Config.OnConfigUpdated += SetupGoalScoreConfig;
        }

        private void SetupGoalScoreConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var goalScoreConfigDict = config.Get<GoalScoreConfig>();
            var goalScoreConfig = goalScoreConfigDict["default"];
            _scoreMultipliers = new GoalScoreRulesDto.ScoreMultipliersLocal(goalScoreConfig.ScoreMultipliers ?? default);
            _baseScores = new GoalScoreRulesDto.BaseScoresLocal(goalScoreConfig.BaseScores ?? default);
            
            _tileStateHpDecreaseScores = new List<TileStateHpScore>();
            for (var i = 0; i < goalScoreConfig.TileStateHpDecreaseScoresLength; i++)
            {
                var tileStateHpDecreaseScore = goalScoreConfig.TileStateHpDecreaseScores(i);
                if(!tileStateHpDecreaseScore.HasValue)
                    continue;
                
                _tileStateHpDecreaseScores.Add(new TileStateHpScore(tileStateHpDecreaseScore.Value));
            }

            _tileStateDestroyScores = new List<TileStateScore>();

            for (var i = 0; i < goalScoreConfig.TileStateDestroyScoresLength; i++)
            {
                var tileStateDestroyScore = goalScoreConfig.TileStateDestroyScores(i);
                if(!tileStateDestroyScore.HasValue)
                    continue;
                
                _tileStateDestroyScores.Add(new TileStateScore(tileStateDestroyScore.Value));
            }

            _tileSpecialityDestroyScores = new Dictionary<TileSpeciality, int>(goalScoreConfig.TileSpecDestroyScoresFbLength);
            for (var i = 0; i < goalScoreConfig.TileSpecDestroyScoresFbLength; i++)
            {
                var tileSpecDestroyScore = goalScoreConfig.TileSpecDestroyScoresFb(i);
                if(!tileSpecDestroyScore.HasValue)
                    continue;
                
                var key = tileSpecDestroyScore.Value.Key.TryParseToEnum<TileSpeciality>();

                if (_tileSpecialityDestroyScores.ContainsKey(key))
                    continue;

                _tileSpecialityDestroyScores[key] = tileSpecDestroyScore.Value.Value;
            }
            
            _tileSpecialityAdjacentDamageScores = new Dictionary<TileSpeciality, int>(goalScoreConfig.TileSpecAdjacentDamageFbLength);
            for (var i = 0; i < goalScoreConfig.TileSpecAdjacentDamageFbLength; i++)
            {
                var tileSpecAdjacentDamage = goalScoreConfig.TileSpecAdjacentDamageFb(i);
                if(!tileSpecAdjacentDamage.HasValue)
                    continue;
                
                var key = tileSpecAdjacentDamage.Value.Key.TryParseToEnum<TileSpeciality>();

                if (_tileSpecialityAdjacentDamageScores.ContainsKey(key))
                    continue;

                _tileSpecialityAdjacentDamageScores[key] = tileSpecAdjacentDamage.Value.Value;
            }
            
            _specialScores = new Dictionary<GoalActionType, int>(goalScoreConfig.SpecialScoresFbLength);
            for (var i = 0; i < goalScoreConfig.SpecialScoresFbLength; i++)
            {
                var specialScore = goalScoreConfig.SpecialScoresFb(i);
                if(!specialScore.HasValue)
                    continue;
                
                var key = specialScore.Value.Key.TryParseToEnum<GoalActionType>();

                if (_specialScores.ContainsKey(key))
                    continue;

                _specialScores[key] = specialScore.Value.Value;
            }
            
            _damageSourceBonusScores = new Dictionary<DamageSource, int>(goalScoreConfig.DamageSourceBonusScoresFbLength);
            for (var i = 0; i < goalScoreConfig.DamageSourceBonusScoresFbLength; i++)
            {
                var damageSourceBonusScore = goalScoreConfig.DamageSourceBonusScoresFb(i);
                if(!damageSourceBonusScore.HasValue)
                    continue;
                
                var key = damageSourceBonusScore.Value.Key.TryParseToEnum<DamageSource>();

                if (_damageSourceBonusScores.ContainsKey(key))
                    continue;

                _damageSourceBonusScores[key] = damageSourceBonusScore.Value.Value;
            }
        }

        public void RefreshForLevel(ILevel level)
        {
            var values = Enum.GetValues(typeof(Stage));
            var firstValue = (int)values.GetValue(0);
            var lastValue = (int)values.GetValue(values.Length - 1);
            _stage = (Stage)Mathf.Clamp(level.Stage, firstValue, lastValue);
        }

        public float ComboMultiplier => _scoreMultipliers.ComboMult;

        public int NormalTile => (int)(_baseScores.NormalTileScore * _scoreMultProvider.GetScoreStageMult(_stage));

        public int CometBaseScore => (int)(_baseScores.CometBaseScore * _scoreMultProvider.GetScoreStageMult(_stage));

        public int BackgroundDamaged => (int)(_baseScores.BackgroundDamagedScore * _scoreMultProvider.GetScoreStageMult(_stage));

        public int BackgroundRemoved => (int)(_baseScores.BackgroundRemovedScore * _scoreMultProvider.GetScoreStageMult(_stage));


        public int GetTileSpecDestroyScore(TileSpeciality tileSpeciality)
        {
            var mult = _scoreMultProvider.GetScoreStageMult(_stage);
            _tileSpecialityDestroyScores.TryGetValue(tileSpeciality, out var score);

            return (int) (mult * score);
        }

        public int GetTileStateDestroyScore(TileState tileState)
        {
            var score = 0;
            foreach (var setting in _tileStateDestroyScores)
            {
                score += (tileState & setting.State) != 0 ? setting.Score : 0;
            }
            
            var mult = _scoreMultProvider.GetScoreStageMult(_stage);
            return (int) (mult * score);
        }
        
        public int GetTileSpecAdjacentDamageScore(TileSpeciality speciality)
        {
            _tileSpecialityAdjacentDamageScores.TryGetValue(speciality, out var score);
            var mult = _scoreMultProvider.GetScoreStageMult(_stage);
            return (int) (mult * score);
        }

        public int GetSpecialScore(GoalActionType goalActionType)
        {
            _specialScores.TryGetValue(goalActionType, out var score);
            var mult = _scoreMultProvider.GetScoreStageMult(_stage);
            return (int) (mult * score);
        }

        public int GetMaxDamageSourceBonusScore(DamageSource damageSource)
        {
            var score = 0;

            foreach (var flag in _damageFlags)
            {
                _damageSourceBonusScores.TryGetValue(flag, out var damageCost);
                var damageScore = (damageSource & flag) != 0 ? damageCost : 0;

                if (damageScore > score)
                    score = damageScore;
            }

            return (int) (_scoreMultProvider.GetScoreStageMult(_stage) * score);
        }

        private struct TileStateScore
        {
            public TileState State;
            public int Score;

            public TileStateScore(FBConfig.TileStateScore configInstance)
            {
                State = configInstance.State.TryParseToEnum<TileState>();
                Score = configInstance.Score;
            }
        }

        private struct TileStateHpScore
        {
            public TileState State;
            public int Hp;
            public int Score;

            public TileStateHpScore(FBConfig.TileStateHpScore configInstance)
            {
                State = configInstance.State.TryParseToEnum<TileState>();
                Score = configInstance.Score;
                Hp = configInstance.Hp;
            }
        }

        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= SetupGoalScoreConfig;
        }
    }
}