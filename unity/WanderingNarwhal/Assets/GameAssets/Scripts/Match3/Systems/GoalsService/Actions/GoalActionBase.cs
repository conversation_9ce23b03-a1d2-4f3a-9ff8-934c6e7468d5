using BBB.Match3.Systems.CreateSimulationSystems.Interfaces;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public abstract class GoalActionBase : IGoalAction
    {
        public virtual bool AssignRandomColor { get { return false; } }
        public int Score { get; protected set; }
        public TileKinds TileKind { get; protected set; }
        public int Backgrounds { get; protected set; }
        public Coords DisplayCoords { get; protected set; }

        public virtual bool ShouldRenderScoresOnBoard => false;
        public virtual bool ShouldAnimateDidi  => false;

        protected GoalActionBase(Coords displayCoords)
        {
            DisplayCoords = displayCoords;
        }

        public virtual float ComboCountToMult(int comboCount)
        {
            return 0f;
        }
        public abstract int GetComboDelta();
        public abstract void Apply(GoalState levelGoalConfig, GoalScoreRules rules, float comboMult);
    }
}
