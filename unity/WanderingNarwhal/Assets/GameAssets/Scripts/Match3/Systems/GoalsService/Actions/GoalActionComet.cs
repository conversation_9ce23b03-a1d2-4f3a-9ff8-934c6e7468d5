using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public class GoalActionComet : GoalActionBase, IGoalGroupOwner
    {
        public override bool AssignRandomColor
        {
            get { return true; }
        }

        private readonly int _index;

        public override bool ShouldRenderScoresOnBoard => false;
        public override bool ShouldAnimateDidi => false;

        public GoalActionComet(int index, Coords displayCoords) : base(displayCoords)
        {
            _index = index;
        }

        public override int GetComboDelta()
        {
            return 0;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            Score = rules.CometBaseScore * (_index+1);
            goalState.Reduce(GoalType.Score, Score);
        }

        public IEnumerable<Coords> AffectedCoords
        {
            get { yield return DisplayCoords; }
        }
    }
}