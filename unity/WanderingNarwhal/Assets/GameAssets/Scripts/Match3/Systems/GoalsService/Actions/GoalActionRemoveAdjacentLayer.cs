using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public class GoalActionRemoveAdjacentLayer : GoalActionBase, IGoalGroupOwner
    {
        private readonly TileSpeciality _tileSpeciality;

        public IEnumerable<Coords> AffectedCoords
        {
            get
            {
                yield return DisplayCoords;
            }
        }


        public GoalActionRemoveAdjacentLayer(TileSpeciality tileSpeciality, Coords displayCoords) : base(displayCoords)
        {
            _tileSpeciality = tileSpeciality;
        }

        public override int GetComboDelta()
        {
            return 0;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            Score = rules.GetTileSpecAdjacentDamageScore(_tileSpeciality);
            goalState.Reduce(GoalType.Score, Score);
        }
    }
}