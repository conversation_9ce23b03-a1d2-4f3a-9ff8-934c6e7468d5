using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.Interfaces
{
    /// <summary>
    /// Goal action for changing level score state based on different in-game events.
    /// </summary>
    public interface IGoalAction
    {
        bool AssignRandomColor { get; }
        bool ShouldRenderScoresOnBoard { get; } 
        bool ShouldAnimateDidi { get; } 
        int Score { get; }
        TileKinds TileKind { get; }
        int Backgrounds { get; }
        Coords DisplayCoords { get; }
        int GetComboDelta();
        float ComboCountToMult(int comboCount);

        /// <summary>
        /// Apply change of level goals score from this action.
        /// </summary>
        void Apply(GoalState levelGoalConfig, GoalScoreRules rules, float comboMult);
    }
}