using System.Collections.Generic;
using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BBB
{
    public abstract class CustomHudManager : BbbMonoBehaviour
    {
        public abstract void Init(IContext context);
        public abstract void Refresh();

        public virtual List<UniTask> GetHudIconsToPreloadTasks(string screenBeingLoaded, ScreenType screenType) => null;

        public bool IsLoading { get; protected set; }
    }
}