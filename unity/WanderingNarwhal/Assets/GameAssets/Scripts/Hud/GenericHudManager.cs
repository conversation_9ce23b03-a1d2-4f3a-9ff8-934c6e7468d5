using System;
using System.Collections.Generic;
using BBB.Controller;
using BBB.Core;
using BBB.Core.AssetBundles;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Modals;
using BBB.UI;
using BBB.UI.Core;
using BBB.Wallet;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Hud;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Tutorial.Core;
using GameAssets.Scripts.UI.Palettes;
using GameAssets.Scripts.Wallet.Visualizing;
using UniRx;
using UnityEngine;

namespace BBB
{
    public class GenericHudManager : BbbMonoBehaviour
    {
        public const string BlockByUpb = "BlockByUpb";
        public const string BlockBySdb = "BlockBySdb";
        public const string BlockByChallengeTrivia = "BlockByChallengeTrivia";

        private readonly List<ScreenType> _screensWithHud = new()
        {
            ScreenType.EpisodeScreen,
            ScreenType.SideMapScreen,
        };

        private enum HudAction
        {
            None,
            Show,
            Hide
        }

        public event Action<bool> HudShown;

        public bool IsHudVisible { get; private set; }

        public TopBarController TopBarController => _topBarController;
        public BottomBarController BottomBarController => _bottomBarController;

        [SerializeField] private Canvas[] _ownCanvases;
        [SerializeField] private BottomBarController _bottomBarController;
        [SerializeField] private GameObject _debugArea;
        [SerializeField] private GameObject _backgroundDownloaderIndicator;
        [SerializeField] private GameObject _errorIndicator;
        [SerializeField] private DebugHudPanel _debugPanel;
        [SerializeField] private TopBarController _topBarController;
        [SerializeField] private GameEventsHudController _gameEventsHudController;

        [SerializeField] private CustomHudManager[] _customHudManagers;
        [SerializeField] private List<ScreenType> _screenTypesToDisableCanvasIn;

        [SerializeField] private PaletteSettings _defaultPalette;
        [field: SerializeField] public SpineDrivenPathHolder StarSpinePath { get; set; }
        [field: SerializeField] public Transform NotReadyTaskHolder { get; set; }
        [field: SerializeField] public Transform SpeechBubblesContainer { get; set; }

        [SerializeField] private GameObject _loadingViewContainer;

        private readonly List<UniTask> _hudIconsToPreloadTasks = new();

        private ITutorialPlaybackController _tutorialPlaybackController;
        private IModalsManager _modalsManager;
        private IScreensBuilder _screensBuilder;
        private IScreensManager _screensManager;
        private IBundleManager _bundleManager;
        private ILockManager _lockManager;
        private IDisposable _forceShowHudHandler;
        private IGameEventManager _gameEventManager;
        private IGameEventResourceManager _eventResourceManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private ScreenType _currentScreenType;

        private HudAction _delayedHudAction;

#if BBB_DEBUG
        private bool _backgroundLoaderVisible;
        private bool _errorIndicatorVisible;
#endif

        private bool _inTransition;
        private bool _hudBlocked;
        private bool _showingWasBlocked;

        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;

        private readonly Dictionary<string, bool> _isBlockedBy = new();
        private bool _lockedByEpisodeIntro;

        public bool IsBlockedByOther => _isBlockedBy.Count > 0;
        public bool IsLoadingElements
        {
            get
            {
                foreach (var manager in _customHudManagers)
                {
                    if (manager != null && manager.IsLoading)
                    {
                        return true;
                    }
                }

                return false;
            }
        }

        public bool IsLocked => (PlayerProfileLocal.IsTutorialEnabled() && _lockManager.IsMapHudLocked()) || _lockedByEpisodeIntro;

        private void Awake()
        {
            _debugPanel.gameObject.SetActive(false);
        }

        public void Init(IContext context, ITutorialPlaybackController tutorialPlaybackController)
        {
            _bottomBarController.Init(context);
            _topBarController.Init(context);
            _gameEventsHudController.Init(context);

            foreach (var customHudManager in _customHudManagers)
            {
                if (customHudManager != null)
                {
                    customHudManager.Init(context);
                }
            }

            _lockManager = context.Resolve<ILockManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _modalsManager = context.Resolve<IModalsManager>();
            _bundleManager = context.Resolve<IBundleManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _eventResourceManager = context.Resolve<IGameEventResourceManager>();
            _tutorialPlaybackController = tutorialPlaybackController;

            _debugPanel.Init(context);
            Subscribe();
#if (!BBB_DEBUG && !BBB_CHEATS) || BBB_TEST
            _debugArea.gameObject.SetActive(false);
#endif
            ResetState();
        }

        private void ResetState()
        {
            IsHudVisible = false;

#if BBB_DEBUG
            _backgroundLoaderVisible = false;
            _errorIndicatorVisible = false;
#endif

            _inTransition = false;
            _hudBlocked = false;
            _showingWasBlocked = false;
            _delayedHudAction = HudAction.None;
            BDebug.LogFormat(LogCat.Hud, "Reset");
        }

        private void Subscribe()
        {
            Unsubscribe();

            _screensManager.OnScreenChangingStarted += TransitionStarted;
            _screensManager.NextScreenPreShown += NextScreenPreShownHandler;
            _screensManager.OnScreenChanged += TransitionEnded;

            _modalsManager.ModalPushed += ModalPushedHandler;
            _modalsManager.ModalShown += ModalShownHandler;
            _modalsManager.ModalHidden += OnModalHidden;

            _forceShowHudHandler = _tutorialPlaybackController.IsTutorialShouldForceShowHUD.Subscribe(TutorialForceShowHUDHandler);
        }

        private void Unsubscribe()
        {
            _screensManager.OnScreenChangingStarted -= TransitionStarted;
            _screensManager.NextScreenPreShown -= NextScreenPreShownHandler;
            _screensManager.OnScreenChanged -= TransitionEnded;

            _modalsManager.ModalPushed -= ModalPushedHandler;
            _modalsManager.ModalHidden -= OnModalHidden;
            _modalsManager.ModalShown -= ModalShownHandler;

            if (_forceShowHudHandler != null)
            {
                _forceShowHudHandler.Dispose();
                _forceShowHudHandler = null;
            }
        }

        private void TutorialForceShowHUDHandler(bool forceShow)
        {
            if (forceShow)
            {
                _delayedHudAction = HudAction.None;

                if (IsHudVisible)
                    return;

                IsHudVisible = true;
                _bottomBarController.Show();
                _topBarController.ShowHud(true);
                ShowHud(true);
                HudShown?.Invoke(true);
            }
            else if (_modalsManager.CurrentActiveModal is { ShouldHideHud: true })
            {
                TryToHide();
            }
        }

        private void ProgressionCheck()
        {
            BlockHud(IsLocked);
        }

        public void ForceShowBottomBar(bool forceShow)
        {
            if (forceShow)
            {
                _bottomBarController.Show();
            }
            else
            {
                _bottomBarController.Hide();
            }
        }

        public void BlockByIntro(bool locked)
        {
            _lockedByEpisodeIntro = locked;
            BlockHud(locked);
        }

        public void BlockHud(bool locked)
        {
            if (!locked && IsLocked)
                return;

            _hudBlocked = locked;

            if (_hudBlocked)
            {
                if (_delayedHudAction is HudAction.Show)
                {
                    _showingWasBlocked = true;
                }

                BDebug.LogFormat(LogCat.Hud, "Call to hide {0}:{1}", Time.frameCount, Time.time);
                _delayedHudAction = HudAction.Hide;
            }
            else if (_showingWasBlocked)
            {
                _showingWasBlocked = false;
                TryToShow();
            }
        }

        private void TransitionStarted(ScreenType screenType, IScreensController controller)
        {
            _inTransition = true;
            _hudBlocked = false;
        }

        private void NextScreenPreShownHandler(ScreenType screenType, IScreensController controller)
        {
            InternalHideHud();
            _currentScreenType = screenType;
            ProgressionCheck();

            var disabledByScreen = ShouldBeDisabledOnCurrentScreen();
            foreach (var canvas in _ownCanvases)
            {
                canvas.enabled = !disabledByScreen;
            }
        }

        private void TransitionEnded(ScreenType screenType, IScreensController controller, IViewPresenter view)
        {
            _inTransition = false;
            TryToShow();
        }

        private bool ShouldBeDisabledOnCurrentScreen()
        {
            return _screenTypesToDisableCanvasIn != null && _screenTypesToDisableCanvasIn.Contains(_screensManager.GetCurrentScreenType()) || !CanHudBeShownOnCurrentScreen();
        }

        private bool CanHudBeShownOnCurrentScreen()
        {
            return _screensWithHud.Contains(_screensManager.GetCurrentScreenType()) && !_screensManager.GetCurrentController().ShouldHideHud;
        }

        private bool IsModalBlockingHud()
        {
            return _modalsManager.ShouldHideHud();
        }

        private bool CanHudBeShown()
        {
            return !_inTransition && !ShouldBeDisabledOnCurrentScreen() && CanHudBeShownOnCurrentScreen() && !IsModalBlockingHud();
        }

        private void ModalPushedHandler(IController controller)
        {
            if (controller.ShouldHideHud && _modalsManager.IsShowingModal(controller.GetType()))
            {
                TryToHide();
            }
        }

        private void ModalShownHandler(IController controller)
        {
            if (controller.ShouldHideHud)
            {
                TryToHide();
            }
        }

        private void OnModalHidden(IController controller)
        {
            TryToShow();
        }

        public void TryToShow()
        {
            BDebug.Log(LogCat.Hud, "TryToShow");

            if (!CanHudBeShown())
                return;

            if (_hudBlocked || IsBlockedByOther)
            {
                _showingWasBlocked = true;
                return;
            }

            BDebug.LogFormat(LogCat.Hud, "Call to show {0}:{1}", Time.frameCount, Time.time);
            _delayedHudAction = HudAction.Show;
        }

        public void TryToHide(bool ignoreForceShowing = false)
        {
            BDebug.Log(LogCat.Hud, $"TryToHide, {ignoreForceShowing}");

            if (!ignoreForceShowing && _tutorialPlaybackController.IsTutorialShouldForceShowHUD.Value)
                return;

            _showingWasBlocked = false;

            BDebug.LogFormat(LogCat.Hud, "Call to hide {0}:{1}", Time.frameCount, Time.time);
            _delayedHudAction = HudAction.Hide;
        }

        private void InternalShowHud()
        {
            BDebug.Log(LogCat.Hud,
                $"Internal Show params: IsHudVisible {IsHudVisible}, _inTransition {_inTransition}, _disabledByScreen {ShouldBeDisabledOnCurrentScreen()}, _hudBlocked {_hudBlocked}, " +
                $"IsBlockedByOther {IsBlockedByOther}, CanHudBeShownOnCurrentScreen {CanHudBeShownOnCurrentScreen()}, IsModalBlockingHud {IsModalBlockingHud()}, BottomBarShown {_bottomBarController.IsShown}");

            if (IsHudVisible)
                return;

            if (_hudBlocked || IsBlockedByOther || !CanHudBeShown())
                return;

            BDebug.LogFormat(LogCat.Hud, "Internal Show {0}:{1}", Time.frameCount, Time.time);

            IsHudVisible = true;
            _bottomBarController.Show();
            _topBarController.ShowHud(true);
            ShowHud(true);
            HudShown?.Invoke(true);
        }

        private void InternalHideHud()
        {
            if (IsHudVisible)
            {
                BDebug.LogFormat(LogCat.Hud, "Internal Hide {0}:{1}", Time.frameCount, Time.time);

                IsHudVisible = false;
                ShowHud(false);
                HudShown?.Invoke(false);
            }

            if (_inTransition)
            {
                var nextScreen = _screensManager.GetTransitionTargetScreenType();

                var shouldBeShownOnNext = (nextScreen & ScreenType.Map) == nextScreen;
                var shouldBeShownOnCurrent = (_currentScreenType & ScreenType.Map) == _currentScreenType;
                var shouldKeepShownBottomBar = shouldBeShownOnNext && shouldBeShownOnCurrent;
                if (!shouldKeepShownBottomBar)
                {
                    _bottomBarController.Hide();
                }
            }
            else
            {
                _bottomBarController.Hide();
            }

            _topBarController.ShowHud(false);
        }

        public void ShowStarsInEditMode()
        {
            var progressBarTransform = _bottomBarController.MapScreenFlowButtons.MapScreenCombinedFlowButtons.ProgressBarTransform;
            _topBarController.ShowStarsInEditMode(progressBarTransform);
            _bottomBarController.MapScreenFlowButtons.MapScreenCombinedFlowButtons.ConfigureProgressBarForTop();
            _bottomBarController.MapScreenFlowButtons.MapScreenCombinedFlowButtons.PlayShineAnimation();
        }

        public void ShowStarsWidget()
        {
            _topBarController.ShowStarsWidget();
        }

        public void HideStarsWidget()
        {
            _topBarController.HideStarsWidget();
            _bottomBarController.MapScreenFlowButtons.MapScreenCombinedFlowButtons.HideSpeechBubbles();
        }

        private void RefreshHud()
        {
            foreach (var customHudManager in _customHudManagers)
            {
                if (customHudManager != null)
                {
                    customHudManager.Refresh();
                }
            }
        }

        private void ShowHud(bool shown)
        {
            if (shown)
            {
                RefreshHud();
            }
#if BBB_DEBUG || BBB_CHEATS
            ResetDebugPanel(shown);
#elif BBB_TEST
            _debugPanel.gameObject.SetActive(false);
            _debugArea.gameObject.SetActive(false);
            _backgroundDownloaderIndicator.gameObject.SetActive(false);
#endif
        }


        // Should not be LateUpdate as it leads to 1 frame delay on animators. To avoid overhead, we disable Canvases of elements
        // So animation can start only after update, if it is invoked from LateUpdate, then it will not be updated same frame leading to glitch as we change parent
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.W))
            {
                var transaction = new Transaction()
                    .AddTag(TransactionTag.Gift)
                    .SetDebug()
                    .Earn("regular", 100);

                WalletTransactionController.MakeTransaction(transaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Gift);
            }

            if (_delayedHudAction is not HudAction.None)
            {
                if (_delayedHudAction is HudAction.Show)
                {
                    InternalShowHud();
                }
                else
                {
                    InternalHideHud();
                }

                _delayedHudAction = HudAction.None;
            }
#if BBB_DEBUG
            UpdateDebugIndicators();
#endif
        }

#if BBB_DEBUG
        private void UpdateDebugIndicators()
        {
            if (IsHudVisible)
            {
                if (_bundleManager.IsBackgroundLoading != _backgroundLoaderVisible)
                {
                    _backgroundDownloaderIndicator.gameObject.SetActive(_bundleManager.IsBackgroundLoading);
                    _backgroundLoaderVisible = _bundleManager.IsBackgroundLoading;
                }

                bool anyErrorPendingToView = BDebug.AnyErrorPendingToView;
                if (anyErrorPendingToView != _errorIndicatorVisible)
                {
                    _errorIndicator.SetActive(anyErrorPendingToView);
                    _errorIndicatorVisible = anyErrorPendingToView;
                }
            }
            else
            {
                if (_backgroundLoaderVisible)
                {
                    _backgroundDownloaderIndicator.gameObject.SetActive(false);
                    _backgroundLoaderVisible = false;
                }

                if (_errorIndicatorVisible)
                {
                    _errorIndicator.SetActive(false);
                    _errorIndicatorVisible = false;
                }
            }
        }
#endif

        private void ResetDebugPanel(bool shown)
        {
            _debugPanel.gameObject.SetActive(false);
            _debugArea.SetActive(shown);
        }

        public void BlockBy(string blockUid, bool block)
        {
            if (block)
            {
                BDebug.Log(LogCat.Hud, $"Hud blocked by {blockUid}");
                _isBlockedBy[blockUid] = true;
            }
            else
            {
                BDebug.Log(LogCat.Hud, $"Hud unblocked by {blockUid}");
                _isBlockedBy.Remove(blockUid);
            }
        }

        public PaletteSettings GetCurrentPalette()
        {
            var gameEvent = GetCurrentEvent();
            if (gameEvent != null)
            {
                var eventSettings = _eventResourceManager.GetSettings(gameEvent.Uid);
                return eventSettings.Palette;
            }

            return _defaultPalette;
        }

        public GameEventBase GetCurrentEvent()
        {
            var currentScreen = _screensBuilder.CurrentScreenType;
            if (currentScreen != ScreenType.SideMapScreen)
                return null;

            return _gameEventManager.GetCurrentSideMapEvent();
        }

        public void SetLoadingViewVisibility(bool visible)
        {
            if (_loadingViewContainer != null)
            {
                _loadingViewContainer.SetActive(visible);
            }
        }

        public List<UniTask> GetHudIconsToPreloadTasksTasks(string screenBeingLoaded, ScreenType screenType)
        {
            _hudIconsToPreloadTasks.Clear();
            foreach (var customHudManager in _customHudManagers)
            {
                if (customHudManager == null)
                    continue;

                var hudIcons = customHudManager.GetHudIconsToPreloadTasks(screenBeingLoaded, screenType);
                if (hudIcons == null)
                    continue;

                _hudIconsToPreloadTasks.AddRange(hudIcons);
            }

            return _hudIconsToPreloadTasks;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
        }
    }
}