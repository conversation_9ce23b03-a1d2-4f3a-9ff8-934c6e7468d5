using System.Collections.Generic;
using BBB.DI;
using BBB.UI;
using GameAssets.Scripts.IAP.Baskets;
using UnityEngine;

namespace BBB
{
    public class BasketHudManager : CustomHudManager
    {
        [SerializeField] private Transform _root;
        [SerializeField] private GameObject _hudItemPrefab;

        private readonly Dictionary<IAPBasket, IAPBasketHud> _activeHudItems = new();

        private IContext _context;
        private IAPBasketManager _iapBasketManager;

        public override void Init(IContext context)
        {
            _iapBasketManager = context.Resolve<IAPBasketManager>();
            _context = context;
        }

        public override void Refresh()
        {
            if (_iapBasketManager == null)
                return;
            
            _iapBasketManager.RefreshAll();
            var allBaskets = _iapBasketManager.GetBaskets();
            if (allBaskets == null)
                return;

            foreach (var basket in allBaskets)
            {
                if (basket.State == IAPBasket.BasketState.NotPurchased)
                {
                    if (_activeHudItems.ContainsKey(basket))
                    {
                        Destroy(_activeHudItems[basket].gameObject);
                        _activeHudItems.Remove(basket);
                    }

                    continue;
                }

                if (_activeHudItems.ContainsKey(basket))
                    continue;
                UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_hudItemPrefab.name}]");
                var go = Instantiate(_hudItemPrefab, _root);
                UnityEngine.Profiling.Profiler.EndSample();
                go.name = "BasketHud: " + basket.Uid;
                go.transform.SetAsLastSibling();

                var hudController = go.GetComponent<IAPBasketHud>();
                hudController.Init(_context);
                hudController.Setup(basket);

                _activeHudItems[basket] = hudController;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _activeHudItems.Clear();
        }
    }
}